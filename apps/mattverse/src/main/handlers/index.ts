/**
 * 主进程处理器统一导出
 */
import { grpcHandlers, initializeGrpcClient, setGrpcClient, getGrpcClient } from './grpc'
import { middlewareHandlers } from './middleware'
import { appHandlers } from './app'
import { cacheHandlers, cacheManager, loggerHandlers } from '@mattverse/electron-core'

// 自定义缓存管理处理器
const customCacheHandlers = {
  ...cacheHandlers,
  // 重写缓存管理器设置更新处理器
  'cache:update-manager-settings': async (settings: any) => {
    try {
      // 更新缓存管理器设置
      cacheManager.updateSettings(settings)
      return { success: true }
    } catch (error) {
      console.error('更新缓存管理器设置失败:', error)
      return { success: false }
    }
  },
}

/**
 * 合并所有处理器
 */
export const handlers = {
  ...grpcHandlers,
  ...middlewareHandlers,
  ...appHandlers,
  ...customCacheHandlers,
  ...loggerHandlers,
}

/**
 * 导出 gRPC 相关工具函数
 */
export { initializeGrpcClient, setGrpcClient, getGrpcClient }

/**
 * 导出各个模块的处理器（如果需要单独使用）
 */
export { grpcHandlers, middlewareHandlers, appHandlers }
