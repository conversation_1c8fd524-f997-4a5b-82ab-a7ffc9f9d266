<template>
  <header
    class="flex h-10 shrink-0 items-center gap-2 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-10 border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
  >
    <div class="flex items-center gap-2 px-2 w-full">
      <!-- 侧边栏触发器和分隔符 -->
      <SidebarTrigger class="-ml-1" />
      <Separator orientation="vertical" class="mr-2 h-4" />

      <!-- 返回按钮 -->
      <Button
        v-if="navbarStore.showBackButton"
        variant="ghost"
        size="sm"
        class="mr-2 h-8 px-2 text-xs"
        @click="handleGoBack"
      >
        <MattIcon name="ArrowLeft" class="h-3 w-3 mr-1" />
        返回
      </Button>

      <!-- 面包屑导航 -->
      <div class="flex items-center gap-1 flex-1 min-w-0">
        <nav class="flex items-center space-x-1 text-sm text-muted-foreground">
          <template v-for="(item, index) in navbarStore.breadcrumbItems" :key="item.title">
            <!-- 面包屑项 -->
            <Button
              v-if="item.path && !item.isActive"
              variant="ghost"
              size="sm"
              class="h-auto p-1 text-xs font-normal text-muted-foreground hover:text-foreground"
              @click="router.push(item.path)"
            >
              {{ item.title }}
            </Button>

            <!-- 当前页面 -->
            <span v-else class="text-xs font-medium text-foreground px-1">
              {{ item.title }}
            </span>

            <!-- 分隔符 -->
            <MattIcon
              v-if="index < navbarStore.breadcrumbItems.length - 1"
              name="ChevronRight"
              class="h-3 w-3 text-muted-foreground/50"
            />
          </template>
        </nav>

        <!-- 工作流标签页 -->
        <WorkflowTabs />
      </div>

      <!-- 语言和主题选择器 -->
      <LanguageThemeSelector />

      <!-- 自定义操作按钮 -->
      <div
        v-if="navbarStore.navbarConfig.showActions && navbarStore.customActions.length > 0"
        class="flex items-center gap-1 ml-2"
      >
        <template v-for="action in navbarStore.customActions" :key="action.id">
          <Button variant="outline" size="sm" class="h-8 px-2 text-xs" @click="action.handler">
            <MattIcon v-if="action.icon" :name="action.icon" class="h-3 w-3 mr-1" />
            {{ action.label }}
          </Button>
        </template>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { useNavbarStore } from '@/store/modules/nav'
import WorkflowTabs from './WorkflowTabs.vue'
import LanguageThemeSelector from './LanguageThemeSelector.vue'

const router = useRouter()
const route = useRoute()
const navbarStore = useNavbarStore()

// 监听路由变化，直接更新面包屑
watch(
  [() => route.path, () => route.meta],
  ([newPath, newMeta]) => {
    navbarStore.updateBreadcrumbs(newPath, newMeta)
  },
  { immediate: true }
)

// 处理返回
const handleGoBack = () => {
  router.back()
}
</script>

<style scoped></style>
