<template>
  <div class="p-6 space-y-6">
    <div class="flex items-center gap-2">
      <h1 class="text-xl font-semibold text-foreground/90">工具模块分组</h1>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-5">
      <Card
        v-for="group in groups"
        :key="group.name"
        class="group hover:shadow-lg transition-all duration-300 relative overflow-hidden border border-border/40 bg-gradient-to-br from-card to-card/95"
      >
        <div
          class="absolute inset-0 bg-gradient-to-r from-primary/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 pointer-events-none"
        ></div>

        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle
            class="text-lg font-medium group-hover:text-primary transition-colors duration-300"
          >
            {{ group.name }}
          </CardTitle>
          <div
            class="rounded-full bg-primary/10 p-2.5 transition-all duration-300 group-hover:scale-110 group-hover:bg-primary/20"
          >
            <MattSvg v-if="isSvg(group.icon)" :name="group.icon.value" class-name="w-5 h-5" />
            <MattIcon
              v-else-if="isIcon(group.icon)"
              :name="group.icon.value"
              class="w-5 h-5 text-primary"
            />
            <span v-else class="text-base">🔧</span>
          </div>
        </CardHeader>

        <CardContent>
          <p
            class="text-sm text-muted-foreground line-clamp-2 h-10 group-hover:text-foreground/90 transition-colors duration-300"
          >
            {{ group.description || '暂无描述' }}
          </p>
          <div class="flex items-center justify-between mt-4">
            <span
              class="text-sm font-medium text-gray-500 group-hover:text-gray-700 transition-colors duration-300"
            >
              是否启用
            </span>
            <Switch
              :checked="!!group.enabled"
              class="transition-all duration-300"
              @update:checked="v => onToggle(group.name, v)"
            />
          </div>
        </CardContent>

        <Badge
          v-if="group.isBuiltin"
          class="badge absolute top-1 left-2 bg-gray-400/80 backdrop-blur-sm transition-all duration-300 group-hover:bg-gray-500"
        >
          内置
        </Badge>
      </Card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useNodeToolsStore } from '@/store'

const toolsStore = useNodeToolsStore()
const { groups } = storeToRefs(toolsStore)

function onToggle(name: string, value: boolean) {
  toolsStore.updateNodeModuleStatus(name, value)
}

function isSvg(icon: any): icon is { type: 'svg'; value: string } {
  return icon && icon.type === 'svg' && typeof icon.value === 'string'
}

function isIcon(icon: any): icon is { type: 'icon'; value: string } {
  return icon && icon.type === 'icon' && typeof icon.value === 'string'
}
</script>

<style lang="scss" scoped>
:deep(.card) {
  @apply transition-all duration-300 hover:-translate-y-[2px] hover:shadow-md;
}
</style>
