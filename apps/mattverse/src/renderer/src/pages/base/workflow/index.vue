<template>
  <div class="h-full flex">
    <!-- 工作流编辑器页面 -->
    <div v-if="isEditorRoute" class="flex-1">
      <WorkflowEditor :workflow-id="workflowId" :workflow-title="workflowTitle" />
    </div>

    <!-- 工作流列表页面 -->
    <template v-else>
      <!-- 左侧文件夹面板 -->
      <div class="w-80 border-r border-border bg-muted/30 hidden md:block">
        <FolderPanel @create-workflow="handleCreateWorkflow" />
      </div>

      <!-- 右侧工作流面板 -->
      <div class="flex-1">
        <WorkflowPanel ref="workflowPanelRef" @open-workflow="handleOpenWorkflow" />
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { FolderPanel, WorkflowPanel } from './components'
import WorkflowEditor from './components/edit/index.vue'
import { type WorkflowItem } from '@/store'
import { usePageNavbar } from '@/composables'
import { useNavbarStore } from '@/store'

// 路由信息
const route = useRoute()
const router = useRouter()
const navbarStore = useNavbarStore()

// 使用 navbar 管理
const navbar = usePageNavbar({
  autoWatch: true,
})

// 当用户访问工作流列表页面时，标记用户已主动访问
watch(
  () => route.path,
  (newPath) => {
    if (newPath === '/workflow') {
      // 用户主动访问了工作流列表页面，清除自动跳转标记
      navbarStore.setWorkflowListVisited(true)
      // 清除所有工作流标签页的选中状态
      navbarStore.clearActiveWorkflowTab()
    }
  },
  { immediate: true }
)

// 检查是否需要自动跳转到活跃的工作流标签页
onMounted(() => {
  // 只有在用户没有主动访问过工作流列表且有活跃标签页时，才自动跳转
  if (
    route.path === '/workflow' &&
    navbarStore.activeWorkflowTab &&
    !navbarStore.workflowListVisited
  ) {
    const activeTab = navbarStore.activeWorkflowTab
    router.replace(activeTab.path)
  }
})

// WorkflowPanel 引用
const workflowPanelRef = ref<InstanceType<typeof WorkflowPanel>>()

const isEditorRoute = computed(() => route.name === 'workflow-editor')
const workflowId = computed(() => route.params.id)
const workflowTitle = computed(() => route.query.title)

// 处理创建工作流
const handleCreateWorkflow = () => {
  // 直接触发 WorkflowPanel 中的创建对话框
  workflowPanelRef.value?.openCreateDialog()
}

// 根据路由状态设置navbar
watch(
  [isEditorRoute, workflowTitle],
  ([isEditor, title]) => {
    if (isEditor) {
      // 编辑器页面
      navbar.setPageInfo('工作流编辑器', title ? `编辑: ${title}` : '编辑工作流')
      navbar.showBackButton()

      // 添加保存按钮
      navbar.addAction({
        id: 'save-workflow',
        label: '保存',
        icon: 'Save',
        variant: 'default',
        onClick: () => {
          // 这里可以调用保存逻辑
          // TODO: 实现保存逻辑
        },
      })
    } else {
      // 列表页面
      navbar.setPageInfo('工作流管理', '管理和编辑您的工作流')
      navbar.hideBackButton()

      // 添加创建按钮
      navbar.addAction({
        id: 'create-workflow',
        label: '新建工作流',
        icon: 'Plus',
        variant: 'default',
        onClick: handleCreateWorkflow,
      })
    }
  },
  { immediate: true }
)

// 处理打开工作流
const handleOpenWorkflow = (workflow: WorkflowItem) => {
  // 添加工作流标签页
  navbar.addWorkflowTab(workflow.id, workflow.title)

  // 工作流已经在 WorkflowPanel 中处理了路由跳转
  // 这里可以添加其他需要的逻辑
}

// 监听路由变化，处理工作流编辑器的标签页
watch(
  () => route.params.id,
  (newId: string | string[]) => {
    if (isEditorRoute.value && newId) {
      // 确保当前编辑的工作流有对应的标签页
      const workflowId = Array.isArray(newId) ? newId[0] : newId
      const existingTab = navbar.navbarStore.workflowTabs.find((tab: any) => tab.id === workflowId)

      if (!existingTab) {
        // 如果没有标签页，创建一个
        const workflowName = (route.query.title as string) || `工作流 ${workflowId}`
        navbar.addWorkflowTab(workflowId, workflowName)
      } else {
        // 如果有标签页，激活它
        navbar.navbarStore.setActiveWorkflowTab(workflowId)
      }
    }
  },
  { immediate: true }
)
</script>

<style scoped></style>
