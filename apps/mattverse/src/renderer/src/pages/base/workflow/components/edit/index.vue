<template>
  <div class="h-full flex flex-col">
    <!-- 编辑器头部 -->
    <div class="flex items-center justify-between p-4 border-b border-border bg-background">
      <div class="flex items-center space-x-4">
        <Button variant="ghost" size="sm" @click="handleBack">
          <MattIcon name="ArrowLeft" class="mr-2 h-4 w-4" />
          返回
        </Button>
        <div class="h-6 w-px bg-border"></div>
        <div>
          <h1 class="text-lg font-semibold">{{ workflowTitle }}</h1>
          <p class="text-sm text-muted-foreground">工作流编辑器</p>
        </div>
      </div>

      <div class="flex items-center space-x-2">
        <Button variant="outline" size="sm">
          <MattIcon name="Save" class="mr-2 h-4 w-4" />
          保存
        </Button>
        <Button size="sm">
          <MattIcon name="Play" class="mr-2 h-4 w-4" />
          运行
        </Button>
      </div>
    </div>

    <!-- 编辑器内容区域 -->
    <div class="flex-1 flex">
      <!-- 左侧工具面板 -->
      <div class="w-64 border-r border-border bg-muted/30">
        <div class="p-4">
          <h3 class="text-sm font-medium mb-3">节点工具</h3>
          <div class="space-y-2">
            <div
              class="p-3 rounded-md border border-border bg-background cursor-pointer hover:bg-accent transition-colors"
            >
              <div class="flex items-center space-x-2">
                <div class="p-1 rounded bg-blue-100">
                  <MattIcon name="Play" class="h-4 w-4 text-blue-600" />
                </div>
                <span class="text-sm">开始节点</span>
              </div>
            </div>
            <div
              class="p-3 rounded-md border border-border bg-background cursor-pointer hover:bg-accent transition-colors"
            >
              <div class="flex items-center space-x-2">
                <div class="p-1 rounded bg-green-100">
                  <MattIcon name="Settings" class="h-4 w-4 text-green-600" />
                </div>
                <span class="text-sm">处理节点</span>
              </div>
            </div>
            <div
              class="p-3 rounded-md border border-border bg-background cursor-pointer hover:bg-accent transition-colors"
            >
              <div class="flex items-center space-x-2">
                <div class="p-1 rounded bg-red-100">
                  <MattIcon name="Square" class="h-4 w-4 text-red-600" />
                </div>
                <span class="text-sm">结束节点</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间画布区域 -->
      <div class="flex-1 relative bg-background">
        <div class="absolute inset-0 flex items-center justify-center">
          <MattEmptyState
            icon="Workflow"
            :icon-size="64"
            title="工作流编辑器"
            description="拖拽左侧节点到画布中开始构建您的工作流"
          >
            <template #action>
              <Button variant="outline">
                <MattIcon name="HelpCircle" class="mr-2 h-4 w-4" />
                查看教程
              </Button>
            </template>
          </MattEmptyState>
        </div>
      </div>

      <!-- 右侧属性面板 -->
      <div class="w-80 border-l border-border bg-muted/30">
        <div class="p-4">
          <h3 class="text-sm font-medium mb-3">属性面板</h3>
          <div class="text-sm text-muted-foreground">选择一个节点来编辑其属性</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useWorkflowStore } from '@/store'
const route = useRoute()
const router = useRouter()
const workflowStore = useWorkflowStore()

// 获取工作流ID
const workflowId = computed(() => route.params.id as string)

// 获取工作流信息
const workflow = computed(() => workflowStore.workflows.find(wf => wf.id === workflowId.value))

const workflowTitle = computed(
  () => workflow.value?.title || (route.query.title as string) || '未知工作流'
)

// 返回工作流列表
const handleBack = () => {
  router.push('/workflow')
}

// 组件挂载时的逻辑
onMounted(async () => {
  // 如果找不到工作流，显示错误并返回
  if (!workflow.value && !route.query.title) {
    await notify.error('工作流不存在')
    handleBack()
  }
})
</script>

<style scoped>
/* 可以添加一些自定义样式 */
</style>
