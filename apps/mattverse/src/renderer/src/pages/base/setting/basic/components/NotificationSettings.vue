<template>
  <div class="space-y-4">
    <div class="flex items-center justify-between">
      <Label class="text-sm font-medium">{{ $t('settings.notifications') }}</Label>
    </div>

    <div class="space-y-4">
      <!-- 应用内通知 (Toaster) -->
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">应用内通知</Label>
          <p class="text-xs text-muted-foreground">在应用内显示 Toast 通知消息</p>
        </div>
        <Switch
          :model-value="notifications.enableNotifications"
          @update:model-value="updateNotification('enableNotifications', $event)"
        />
      </div>

      <Separator />

      <!-- 系统通知 (桌面通知) -->
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">系统通知</Label>
          <p class="text-xs text-muted-foreground">在系统桌面显示通知弹窗</p>
        </div>
        <Switch
          :model-value="notifications.desktopNotifications"
          @update:model-value="updateNotification('desktopNotifications', $event)"
        />
      </div>

      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">{{
            $t('settings.notification_options.sound_notifications')
          }}</Label>
          <p class="text-xs text-muted-foreground">
            {{ $t('settings.notification_options.sound_notifications_desc') }}
          </p>
        </div>
        <Switch
          :model-value="notifications.soundNotifications"
          @update:model-value="updateNotification('soundNotifications', $event)"
        />
      </div>

      <Separator />

      <!-- 测试通知功能 -->
      <div class="flex items-center justify-between">
        <div class="space-y-0.5">
          <Label class="text-sm">测试通知功能</Label>
          <p class="text-xs text-muted-foreground">点击按钮测试当前通知设置是否正常工作</p>
        </div>
        <Button
          variant="outline"
          size="sm"
          :disabled="!notifications.enableNotifications && !notifications.desktopNotifications"
          @click="testNotification"
        >
          测试通知
        </Button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSettingsStore, type NotificationSettings } from '@/store'
import { notify, notificationSounds, getNotificationService } from '@mattverse/shared'

const settingsStore = useSettingsStore()

const notifications = computed(() => settingsStore.notifications)

const updateNotification = (key: keyof NotificationSettings, value: boolean) => {
  // 更新 store 中的设置
  settingsStore.updateNotifications({ [key]: value })

  // 同步更新通知服务的设置
  const notificationService = getNotificationService()
  if (notificationService) {
    notificationService.updateSettings({ [key]: value })
  }

  // 更新声音通知设置
  if (key === 'soundNotifications') {
    notificationSounds.setEnabled(value)
  }

  // 当启用系统通知时，显示提示
  if (key === 'desktopNotifications' && value) {
    notify.success('系统通知已启用', {
      description: '您将收到桌面通知',
      showDesktop: false,
    })
  }
}

// 测试通知功能
const testNotification = async () => {
  await notify.test()
}
</script>
