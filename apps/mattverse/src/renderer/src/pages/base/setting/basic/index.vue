<template>
  <div class="flex flex-col h-[calc(100vh-3rem)] overflow-hidden">
    <!-- 页面标题和操作按钮 -->
    <div class="flex-shrink-0 px-6 py-4 border-0 bg-background">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold tracking-tight">{{ $t('settings.title') }}</h1>
          <p class="text-muted-foreground text-sm mt-1">
            {{ $t('settings.basic_desc') }}
          </p>
        </div>

        <!-- 操作按钮 -->
        <div class="flex items-center space-x-2">
          <Button variant="outline" size="sm" @click="exportSettings">
            <MattIcon name="Download" class="mr-2 h-4 w-4" />
            {{ $t('settings.export_settings') }}
          </Button>
          <Button
            v-for="action in actionButtons"
            :key="action.key"
            :variant="action.variant"
            size="sm"
            @click="action.handler"
          >
            <MattIcon :name="action.icon" class="mr-2 h-4 w-4" />
            {{ action.label }}
          </Button>
        </div>
      </div>
    </div>

    <!-- 设置内容 - 可滚动区域 -->
    <div class="flex-1 min-h-0 overflow-y-auto overflow-x-hidden scrollbar">
      <div class="container mx-auto max-w-7xl p-6">
        <!-- 基于内容复杂度的智能响应式布局 -->
        <div class="space-y-6">
          <!-- 第一行：简单设置组合 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- 语言设置 - 独立卡片 -->
            <Card
              class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
            >
              <CardHeader>
                <CardTitle class="flex items-center space-x-2">
                  <MattIcon name="Globe" class="h-5 w-5" />
                  <span>{{ $t('settings.language') }}</span>
                </CardTitle>
                <CardDescription>
                  {{ $t('settings.language_desc') }}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <LanguageSelector />
              </CardContent>
            </Card>

            <!-- 通知设置 -->
            <Card
              class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
            >
              <CardHeader>
                <CardTitle class="flex items-center space-x-2">
                  <MattIcon name="Bell" class="h-5 w-5" />
                  <span>{{ $t('settings.notifications') }}</span>
                </CardTitle>
                <CardDescription>
                  {{ $t('settings.notifications_desc') }}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <NotificationSettings />
              </CardContent>
            </Card>

            <!-- 高级设置 - 紧凑版 -->
            <Card
              class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
            >
              <CardHeader>
                <CardTitle class="flex items-center space-x-2">
                  <MattIcon name="Settings" class="h-5 w-5" />
                  <span>{{ $t('settings.advanced') }}</span>
                </CardTitle>
                <CardDescription>
                  {{ $t('settings.advanced_desc') }}
                </CardDescription>
              </CardHeader>
              <CardContent class="space-y-4">
                <!-- 自动保存 -->
                <div class="flex items-center justify-between">
                  <div class="space-y-0.5">
                    <Label class="text-sm">{{ $t('settings.advanced_options.auto_save') }}</Label>
                    <p class="text-xs text-muted-foreground">
                      {{ $t('settings.advanced_options.auto_save_desc') }}
                    </p>
                  </div>
                  <Switch
                    :model-value="advanced.autoSave"
                    @update:model-value="updateAdvanced('autoSave', $event)"
                  />
                </div>

                <Separator />

                <!-- 调试模式 -->
                <div class="flex items-center justify-between">
                  <div class="space-y-0.5">
                    <Label class="text-sm">{{ $t('settings.advanced_options.debug_mode') }}</Label>
                    <p class="text-xs text-muted-foreground">
                      {{ $t('settings.advanced_options.debug_mode_desc') }}
                    </p>
                  </div>
                  <Switch
                    :model-value="advanced.debugMode"
                    @update:model-value="updateAdvanced('debugMode', $event)"
                  />
                </div>
              </CardContent>
            </Card>
          </div>

          <!-- 第二行：主题和字体设置 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 主题设置 -->
            <Card
              class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
            >
              <CardHeader>
                <CardTitle class="flex items-center space-x-2">
                  <MattIcon name="Palette" class="h-5 w-5" />
                  <span>{{ $t('settings.theme') }}</span>
                </CardTitle>
                <CardDescription>
                  {{ $t('settings.theme_desc') }}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ThemeSelector />
              </CardContent>
            </Card>

            <!-- 字体设置 -->
            <Card
              class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
            >
              <CardHeader>
                <CardTitle class="flex items-center space-x-2">
                  <MattIcon name="Type" class="h-5 w-5" />
                  <span>{{ $t('settings.font') }}</span>
                </CardTitle>
                <CardDescription>
                  {{ $t('settings.font_desc') }}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <FontSelector />
              </CardContent>
            </Card>
          </div>

          <!-- 第三行：缓存设置 -->
          <Card
            class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
          >
            <CardHeader>
              <CardTitle class="flex items-center space-x-2">
                <MattIcon name="Database" class="h-5 w-5" />
                <span>{{ $t('settings.cache') }}</span>
              </CardTitle>
              <CardDescription>
                {{ $t('settings.cache_desc') }}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <CacheSettings />
            </CardContent>
          </Card>

          <!-- 第四行：日志设置 -->
          <Card
            class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
          >
            <CardHeader>
              <CardTitle class="flex items-center space-x-2">
                <MattIcon name="FileText" class="h-5 w-5" />
                <span>{{ $t('settings.log_options.title') }}</span>
              </CardTitle>
              <CardDescription>
                {{ $t('settings.log_options.description') }}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <LogSettings />
            </CardContent>
          </Card>

          <!-- 第五行：高级设置详细配置 -->
          <Card
            class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1"
          >
            <CardHeader>
              <CardTitle class="flex items-center space-x-2">
                <MattIcon name="Cog" class="h-5 w-5" />
                <span>{{ $t('settings.advanced_detailed') }}</span>
              </CardTitle>
              <CardDescription>
                {{ $t('settings.advanced_detailed_desc') }}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- 备份设置 -->
                <div class="space-y-4">
                  <Label class="text-base font-medium">{{ $t('settings.backup_settings') }}</Label>

                  <div class="space-y-3">
                    <Label class="text-sm">{{
                      $t('settings.advanced_options.backup_frequency')
                    }}</Label>
                    <Select
                      :model-value="advanced.backupFrequency"
                      @update:model-value="updateAdvanced('backupFrequency', $event)"
                    >
                      <SelectTrigger class="w-full">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem
                          v-for="option in backupOptions"
                          :key="option.value"
                          :value="option.value"
                        >
                          {{ option.label }}
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <!-- 性能设置 -->
                <div class="space-y-4">
                  <Label class="text-base font-medium">{{
                    $t('settings.performance_settings')
                  }}</Label>

                  <div class="space-y-3">
                    <Label class="text-sm"
                      >{{ $t('settings.advanced_options.cache_size') }} (MB)</Label
                    >
                    <div class="space-y-2">
                      <Slider
                        :model-value="[advanced.cacheSize]"
                        :max="500"
                        :min="50"
                        :step="10"
                        class="flex-1"
                        @update:model-value="updateAdvanced('cacheSize', $event[0])"
                      />
                      <div class="flex justify-between text-xs text-muted-foreground">
                        <span>50MB</span>
                        <span class="font-medium">{{ advanced.cacheSize }}MB</span>
                        <span>500MB</span>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 开发者选项 -->
                <div class="space-y-4">
                  <Label class="text-base font-medium">{{
                    $t('settings.developer_options')
                  }}</Label>

                  <div class="space-y-3">
                    <!-- 调试模式 -->
                    <div class="flex items-center justify-between">
                      <div class="space-y-0.5">
                        <Label class="text-sm">{{
                          $t('settings.advanced_options.debug_mode')
                        }}</Label>
                        <p class="text-xs text-muted-foreground">
                          {{ $t('settings.advanced_options.debug_mode_desc') }}
                        </p>
                      </div>
                      <Switch
                        :model-value="advanced.debugMode"
                        @update:model-value="updateAdvanced('debugMode', $event)"
                      />
                    </div>

                    <Separator />

                    <!-- 自动保存 -->
                    <div class="flex items-center justify-between">
                      <div class="space-y-0.5">
                        <Label class="text-sm">{{
                          $t('settings.advanced_options.auto_save')
                        }}</Label>
                        <p class="text-xs text-muted-foreground">
                          {{ $t('settings.advanced_options.auto_save_desc') }}
                        </p>
                      </div>
                      <Switch
                        :model-value="advanced.autoSave"
                        @update:model-value="updateAdvanced('autoSave', $event)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useI18n } from '@mattverse/i18n'
import { useSettingsStore, type AdvancedSettings } from '@/store'
import {
  LanguageSelector,
  ThemeSelector,
  FontSelector,
  CacheSettings,
  NotificationSettings,
  LogSettings,
} from './components'

const { t } = useI18n()
const settingsStore = useSettingsStore()

const advanced = computed(() => settingsStore.advanced)

const backupOptions = computed(() => [
  { value: 'never', label: t('settings.backup_options.never') },
  { value: 'daily', label: t('settings.backup_options.daily') },
  { value: 'weekly', label: t('settings.backup_options.weekly') },
  { value: 'monthly', label: t('settings.backup_options.monthly') },
])

const actionButtons = computed(() => [
  {
    key: 'reset',
    variant: 'outline' as const,
    icon: 'RotateCcw',
    label: t('settings.reset_to_defaults'),
    handler: resetSettings,
  },
  {
    key: 'save',
    variant: 'default' as const,
    icon: 'Save',
    label: t('common.save'),
    handler: saveSettings,
  },
])

const updateAdvanced = (key: keyof AdvancedSettings, value: any) => {
  settingsStore.updateAdvanced({ [key]: value })
}

const exportSettings = () => {
  try {
    const settings = settingsStore.exportSettings()
    const dataStr = JSON.stringify(settings, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })

    const link = document.createElement('a')
    link.href = URL.createObjectURL(dataBlob)
    link.download = `mattverse-settings-${new Date().toISOString().split('T')[0]}.json`
    link.click()

    console.log(t('settings.export_success'))
  } catch {
    console.error(t('settings.export_failed'))
  }
}

const resetSettings = () => {
  settingsStore.resetToDefaults()
  console.log(t('settings.reset_success'))
}

const saveSettings = () => {
  // 设置会自动持久化，这里只是给用户反馈
  console.log(t('settings.save_success'))
}
</script>
