<template>
  <div class="space-y-6">
    <!-- 主题模式选择 -->
    <div class="space-y-3">
      <div class="flex items-center justify-between">
        <Label class="text-sm font-medium">{{ $t('settings.theme') }}</Label>
        <Button variant="ghost" size="sm" @click="toggleTheme">
          <MattIcon :name="currentThemeIcon" class="h-4 w-4" />
        </Button>
      </div>

      <div class="grid grid-cols-3 gap-3">
        <div v-for="themeOption in themeOptions" :key="themeOption.value" class="relative">
          <input
            :id="`theme-${themeOption.value}`"
            v-model="selectedTheme"
            :value="themeOption.value"
            type="radio"
            name="theme"
            class="peer sr-only"
          />
          <label
            :for="`theme-${themeOption.value}`"
            :class="[
              'group flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 p-4 transition-all duration-200 hover:shadow-md peer-checked:border-primary peer-checked:shadow-lg',
              // 根据主题类型应用不同的背景和边框样式
              themeOption.value === 'light'
                ? 'bg-gradient-to-br from-orange-50 via-yellow-25 to-orange-100 border-orange-200 hover:border-orange-300 peer-checked:bg-orange-50 peer-checked:border-orange-400'
                : themeOption.value === 'dark'
                  ? 'bg-gradient-to-br from-slate-800 via-slate-850 to-slate-900 border-slate-600 hover:border-slate-500 peer-checked:bg-slate-800 peer-checked:border-slate-400 text-slate-100'
                  : 'bg-gradient-to-br from-gray-100 via-gray-150 to-gray-200 border-gray-300 hover:border-gray-400 peer-checked:bg-gray-100 peer-checked:border-gray-500',
            ]"
          >
            <!-- 主题图标预览 -->
            <div
              class="mb-3 flex h-12 w-16 items-center justify-center rounded-lg shadow-sm bg-white/10 backdrop-blur-sm"
            >
              <MattIcon
                :name="themeOption.icon"
                :class="[
                  'h-5 w-5',
                  themeOption.value === 'light'
                    ? 'text-orange-600'
                    : themeOption.value === 'dark'
                      ? 'text-blue-300'
                      : 'text-gray-600',
                ]"
              />
            </div>

            <span
              :class="[
                'text-sm font-medium transition-colors peer-checked:text-primary',
                themeOption.value === 'dark'
                  ? 'text-slate-100 group-hover:text-slate-50'
                  : 'text-foreground group-hover:text-primary',
              ]"
            >
              {{ themeOption.label }}
            </span>

            <!-- 选中指示器 -->
            <div
              v-if="selectedTheme === themeOption.value"
              :class="[
                'absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full',
                themeOption.value === 'dark'
                  ? 'bg-blue-400 text-slate-900'
                  : 'bg-primary text-primary-foreground',
              ]"
            >
              <MattIcon name="Check" class="h-3 w-3" />
            </div>
          </label>
        </div>
      </div>
    </div>

    <!-- 主题色调选择 -->
    <div class="space-y-3">
      <Label class="text-sm font-medium">{{ $t('settings.theme_colors') }}</Label>
      <div class="grid grid-cols-3 gap-3">
        <div v-for="colorTheme in colorThemes" :key="colorTheme.value" class="relative">
          <div
            @click="toggleColorTheme(colorTheme.value)"
            class="group flex cursor-pointer flex-col items-center justify-center rounded-lg border-2 border-gray-200 bg-card p-3 transition-all duration-200 dark:border-gray-700"
            :class="{
              'border-primary bg-primary/5 shadow-lg': selectedColorTheme === colorTheme.value,
            }"
          >
            <!-- 颜色背景预览 -->
            <div
              class="mb-2 h-8 w-12 rounded-lg shadow-sm transition-all duration-200 relative overflow-hidden"
              :style="{
                backgroundColor: colorTheme.previewColor,
                boxShadow: `inset 0 1px 2px rgba(0,0,0,0.1), 0 1px 3px rgba(0,0,0,0.1)`,
              }"
            >
              <!-- 添加一些装饰性元素 -->
              <div
                class="absolute inset-0 opacity-20"
                :style="{
                  background: `radial-gradient(circle at 30% 30%, rgba(255,255,255,0.3), transparent 50%)`,
                }"
              />
            </div>
            <span
              class="text-xs font-medium text-center transition-colors"
              :class="{
                'text-primary': selectedColorTheme === colorTheme.value,
              }"
            >
              {{ getColorThemeLabel(colorTheme.value) }}
            </span>

            <!-- 选中指示器 -->
            <div
              v-if="selectedColorTheme === colorTheme.value"
              class="absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full bg-primary text-primary-foreground"
            >
              <MattIcon name="Check" class="h-2.5 w-2.5" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 当前主题显示 -->
    <div v-if="currentTheme !== selectedTheme" class="rounded-lg bg-muted/50 p-3">
      <div class="flex items-center space-x-2 text-sm text-muted-foreground">
        <MattIcon :name="currentThemeIcon" class="h-4 w-4" />
        <span
          >{{ $t('settings.theme_options.current_display') }}: {{ getCurrentThemeLabel() }}</span
        >
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { useI18n } from '@mattverse/i18n'
import { useSettingsStore, type ThemeMode, type ColorTheme } from '@/store'

const { t } = useI18n()
const settingsStore = useSettingsStore()

const selectedTheme = computed({
  get: () => settingsStore.theme,
  set: (value: ThemeMode) => settingsStore.setTheme(value),
})

const selectedColorTheme = computed({
  get: () => settingsStore.colorTheme,
  set: (value: ColorTheme) => settingsStore.setColorTheme(value),
})

const currentTheme = computed(() => settingsStore.currentTheme)

const currentThemeIcon = computed(() => {
  const option = themeOptions.value.find(opt => opt.value === currentTheme.value)
  return option?.icon || 'Monitor'
})

const themeOptions = computed(() => [
  {
    value: 'light' as ThemeMode,
    label: t('settings.theme_options.light'),
    icon: 'Sun',
  },
  {
    value: 'dark' as ThemeMode,
    label: t('settings.theme_options.dark'),
    icon: 'Moon',
  },
  {
    value: 'system' as ThemeMode,
    label: t('settings.theme_options.system'),
    icon: 'Monitor',
  },
])

// 色彩主题配置（只保留预览颜色，移除重复的 primary 配置）
const colorThemeConfigs = [
  // 浅色主题
  { value: 'city-light', previewColor: '#d8e8e7', type: 'light' },
  { value: 'forest-light', previewColor: '#e0d6af', type: 'light' },
  { value: 'lake-light', previewColor: '#cbcad9', type: 'light' },
  { value: 'desert-light', previewColor: '#fdcdb0', type: 'light' },
  { value: 'farm-light', previewColor: '#c8d3ec', type: 'light' },
  { value: 'garden-light', previewColor: '#fee1b2', type: 'light' },
  // 深色主题
  { value: 'city-dark', previewColor: '#2a3b42', type: 'dark' },
  { value: 'forest-dark', previewColor: '#2c3320', type: 'dark' },
  { value: 'lake-dark', previewColor: '#1e2a4a', type: 'dark' },
  { value: 'desert-dark', previewColor: '#3d2b1f', type: 'dark' },
  { value: 'farm-dark', previewColor: '#2a2d1e', type: 'dark' },
  { value: 'garden-dark', previewColor: '#2d1f2a', type: 'dark' },
] as const

// 根据当前主题模式过滤色调选项
const colorThemes = computed(() => {
  const currentThemeMode = currentTheme.value
  return colorThemeConfigs.filter(theme => theme.type === currentThemeMode)
})

const getCurrentThemeLabel = () => {
  const option = themeOptions.value.find(opt => opt.value === currentTheme.value)
  return option?.label || currentTheme.value
}

const toggleTheme = () => {
  settingsStore.toggleTheme()
}

// 切换色彩主题（支持取消选中）
const toggleColorTheme = (themeValue: string) => {
  const colorTheme = themeValue as ColorTheme
  if (selectedColorTheme.value === colorTheme) {
    selectedColorTheme.value = 'neutral'
  } else {
    selectedColorTheme.value = colorTheme
  }
}

// 获取色彩主题标签（简化版）
const getColorThemeLabel = (themeValue: string) => {
  return t(`settings.color_themes.${themeValue}`) || themeValue
}

// 监听主题模式变化，自动切换对应的色调（仅在已选择具体色彩时）
watch(currentTheme, (newTheme, oldTheme) => {
  if (newTheme !== oldTheme && selectedColorTheme.value !== 'neutral') {
    const currentColorTheme = selectedColorTheme.value
    const currentThemeBase = currentColorTheme.replace(/-light|-dark$/, '')
    const newColorTheme = `${currentThemeBase}-${newTheme}` as ColorTheme

    // 检查新的色调是否存在
    const themeExists = colorThemeConfigs.some(theme => theme.value === newColorTheme)
    if (themeExists) {
      selectedColorTheme.value = newColorTheme
    }
    // 如果不存在对应的色调，保持当前选择不变
  }
})
</script>
