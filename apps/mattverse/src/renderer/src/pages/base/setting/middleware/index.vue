<template>
  <div class="flex flex-col h-[calc(100vh-3rem)]overflow-hidden">
    <!-- 页面标题和操作按钮 -->
    <div class="flex-shrink-0 px-6 py-4 border-0 bg-background">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold tracking-tight">{{ $t('settings.middleware.title') }}</h1>
          <p class="text-muted-foreground text-sm mt-1">
            {{ $t('settings.middleware.subtitle') }}
          </p>
        </div>
      </div>
    </div>

    <!-- 中台设置内容 - 可滚动区域 -->
    <div class="flex-1 min-h-0 overflow-y-auto overflow-x-hidden">
      <div class="container mx-auto max-w-none p-8">
        <!-- 响应式Grid布局 -->
        <div
          class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 auto-rows-max max-w-[1600px] mx-auto"
        >
          <!-- 连接配置卡片 -->
          <ConnectionConfig
            :model-value="middlewareStore.config"
            :has-changes="middlewareStore.hasChanges"
            :is-connecting="middlewareStore.isConnecting"
            @update:model-value="middlewareStore.updateConfig"
            @test-connection="testConnection"
            @save-config="saveConfig"
          />

          <!-- 连接状态卡片 -->
          <ConnectionStatus
            :connection-status="middlewareStore.connectionStatus"
            :current-address="middlewareStore.currentAddress"
            :middleware-version="middlewareStore.middlewareVersion"
          />

          <!-- 高级设置卡片 -->
          <AdvancedSettings
            :config="middlewareStore.config"
            @update:config="middlewareStore.updateConfig"
          />
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <Dialog :open="showConfirmDialog" @update:open="showConfirmDialog = $event">
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{{ $t('settings.middleware.confirm_changes') }}</DialogTitle>
          <DialogDescription>
            {{ $t('settings.middleware.confirm_changes_desc') }}
          </DialogDescription>
        </DialogHeader>
        <div class="space-y-4">
          <div class="bg-muted/50 p-4 rounded-lg space-y-2">
            <div class="flex justify-between">
              <span class="text-sm text-muted-foreground"
                >{{ $t('settings.middleware.new_host') }}:</span
              >
              <span class="text-sm font-medium">{{ middlewareStore.config.host }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-sm text-muted-foreground"
                >{{ $t('settings.middleware.new_port') }}:</span
              >
              <span class="text-sm font-medium">{{ middlewareStore.config.port }}</span>
            </div>
          </div>
          <p class="text-sm text-muted-foreground">
            {{ $t('settings.middleware.restart_warning') }}
          </p>
        </div>
        <DialogFooter>
          <Button variant="outline" @click="showConfirmDialog = false">
            {{ $t('common.cancel') }}
          </Button>
          <Button @click="confirmSave" :disabled="isSaving">
            <MattIcon v-if="isSaving" name="Loader2" class="mr-2 h-4 w-4 animate-spin" />
            {{ $t('common.confirm') }}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useI18n } from '@mattverse/i18n'
import { useMiddlewareStore } from '@/store'
import { ConnectionConfig, ConnectionStatus, AdvancedSettings } from './components'

// 国际化
const { t } = useI18n()

// 使用middleware store
const middlewareStore = useMiddlewareStore()

// 本地状态
const showConfirmDialog = ref(false)
const isSaving = ref(false)

// 测试连接
const testConnection = async () => {
  try {
    const success = await middlewareStore.testConnection()
    if (success) {
      await notify.success(t('settings.middleware.test_connection_success'))
    } else {
      await notify.error(t('settings.middleware.test_connection_failed'))
    }
  } catch (error) {
    console.error('测试连接失败:', error)
    await notify.error(t('settings.middleware.test_connection_failed'))
  }
}

// 保存配置
const saveConfig = () => {
  if (middlewareStore.hasChanges) {
    showConfirmDialog.value = true
  }
}

// 确认保存
const confirmSave = async () => {
  isSaving.value = true

  try {
    await middlewareStore.saveConfig()
    await notify.success(t('settings.middleware.config_saved'))

    // 调用重启应用
    const result = await middlewareStore.restartApp()
    if (result.success) {
      if (result.isDevelopment) {
        // 开发环境提示应用即将关闭
        await notify.info(result.message || t('settings.middleware.app_will_close'), {
          duration: 5000,
        })
      } else {
        // 生产环境自动重启
        await notify.info(result.message || t('settings.middleware.restarting_app'))
      }
    } else {
      await notify.error(result.error || t('settings.middleware.restart_failed'))
    }
  } catch (error) {
    console.error('保存配置失败:', error)
    await notify.error(t('settings.middleware.config_save_failed'))
  }

  isSaving.value = false
  showConfirmDialog.value = false
}

// 监听配置变化
watch(
  () => [middlewareStore.config.host, middlewareStore.config.port],
  (newValues, oldValues) => {
    // 只有当 host 或 port 真正发生变化时才重置连接状态
    if (oldValues && (newValues[0] !== oldValues[0] || newValues[1] !== oldValues[1])) {
      if (middlewareStore.connectionStatus === 'READY') {
        middlewareStore.setConnectionStatus('IDLE')
      }
      // 重置连接中状态，确保测试按钮可以点击
      middlewareStore.setConnecting(false)
    }
  }
)

// 组件挂载时加载配置
onMounted(async () => {
  try {
    await middlewareStore.loadConfig()
    // 检查gRPC连接状态
    await middlewareStore.checkGrpcStatus()
    // 如果连接正常，获取版本信息
    if (middlewareStore.isConnected) {
      await middlewareStore.getVersion()
    }
  } catch (error) {
    console.error('初始化中台设置失败:', error)
    await notify.error(t('settings.middleware.init_failed'))
  }
})
</script>
