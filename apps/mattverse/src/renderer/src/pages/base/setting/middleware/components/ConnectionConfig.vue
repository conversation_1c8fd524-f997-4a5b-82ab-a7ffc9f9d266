<template>
  <Card class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1">
    <CardHeader>
      <CardTitle class="flex items-center space-x-2">
        <MattIcon name="Settings" class="h-5 w-5" />
        <span>{{ $t('settings.middleware.connection_config') }}</span>
      </CardTitle>
      <CardDescription>
        {{ $t('settings.middleware.connection_config_desc') }}
      </CardDescription>
    </CardHeader>
    <CardContent class="space-y-6">
      <!-- 服务器地址 -->
      <div class="space-y-2">
        <Label for="host">{{ $t('settings.middleware.host') }}</Label>
        <Input
          id="host"
          :model-value="props.modelValue.host"
          :placeholder="$t('settings.middleware.host_placeholder')"
          :class="[
            'w-full',
            hostError ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''
          ]"
          @update:model-value="updateHost"
        />
        <p v-if="hostError" class="text-sm text-red-500 mt-1">
          {{ hostError }}
        </p>
      </div>

      <!-- 端口 -->
      <div class="space-y-2">
        <Label for="port">{{ $t('settings.middleware.port') }}</Label>
        <Input
          id="port"
          :model-value="props.modelValue.port"
          type="number"
          :placeholder="$t('settings.middleware.port_placeholder')"
          class="w-full"
          @update:model-value="updatePort"
        />
      </div>

      <!-- 操作按钮 -->
      <div class="flex space-x-3 pt-4">
        <Button
          @click="$emit('test-connection')"
          variant="outline"
          :disabled="!canTestConnection"
          class="flex items-center space-x-2"
        >
          <MattIcon name="Zap" class="h-4 w-4" />
          <span>{{ $t('settings.middleware.test_connection') }}</span>
        </Button>
        <Button
          @click="$emit('save-config')"
          :disabled="!canSave"
          class="flex items-center space-x-2"
        >
          <MattIcon name="Save" class="h-4 w-4" />
          <span>{{ $t('common.save') }}</span>
        </Button>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useI18n } from '@mattverse/i18n'
import type { MiddlewareConfig } from '@/store'

interface Props {
  modelValue: MiddlewareConfig
  hasChanges: boolean
  isConnecting: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: MiddlewareConfig]
  'test-connection': []
  'save-config': []
}>()

// 国际化
const { t } = useI18n()

// 主机地址校验状态
const hostError = ref<string>('')

// 校验主机地址格式
const validateHost = (host: string): boolean => {
  if (!host.trim()) {
    hostError.value = t('settings.middleware.host_required')
    return false
  }

  // IP地址正则表达式
  const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  
  // 域名正则表达式（支持子域名）
  const domainRegex = /^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/
  
  // localhost 特殊处理
  if (host === 'localhost') {
    hostError.value = ''
    return true
  }

  if (ipRegex.test(host) || domainRegex.test(host)) {
    hostError.value = ''
    return true
  }

  hostError.value = t('settings.middleware.host_invalid')
  return false
}

// 计算是否可以保存（主机地址有效即可保存）
const canSave = computed(() => {
  return !hostError.value && props.modelValue.host.trim() && props.modelValue.port > 0
})

// 计算是否可以测试连接（主机地址有效且不在连接中）
const canTestConnection = computed(() => {
  const hasValidHost = props.modelValue.host.trim().length > 0
  const hasValidPort = props.modelValue.port > 0
  const notConnecting = !props.isConnecting
  const noHostError = !hostError.value
  
  // 临时调试信息
  console.log('测试连接按钮状态检查:', {
    hasValidHost,
    hasValidPort,
    notConnecting,
    noHostError,
    host: props.modelValue.host,
    port: props.modelValue.port,
    isConnecting: props.isConnecting,
    hostError: hostError.value,
    canTest: notConnecting && noHostError && hasValidHost && hasValidPort
  })
  
  return notConnecting && noHostError && hasValidHost && hasValidPort
})

// 更新主机地址
const updateHost = (value: string) => {
  // 移除多余的空格
  const trimmedValue = value.trim()
  
  // 校验地址格式
  validateHost(trimmedValue)
  
  emit('update:modelValue', {
    ...props.modelValue,
    host: trimmedValue
  })
}

// 更新端口
const updatePort = (value: string | number) => {
  const port = typeof value === 'string' ? parseInt(value) || 0 : value
  emit('update:modelValue', {
    ...props.modelValue,
    port: port
  })
}

// 组件挂载时验证初始主机地址
onMounted(() => {
  if (props.modelValue.host) {
    validateHost(props.modelValue.host)
  }
})

// 监听主机地址变化，自动验证
watch(() => props.modelValue.host, (newHost) => {
  if (newHost) {
    validateHost(newHost)
  } else {
    hostError.value = ''
  }
})
</script>
