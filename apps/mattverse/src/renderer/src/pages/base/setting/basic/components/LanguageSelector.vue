<template>
  <div class="space-y-3">
    <div class="flex items-center justify-between">
      <Label class="text-sm font-medium">{{ $t('settings.language') }}</Label>
    </div>

    <Select :model-value="currentLanguage" @update:model-value="handleLanguageChange">
      <SelectTrigger class="w-full">
        <SelectValue :placeholder="$t('components.language_selector.title')" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem v-for="locale in SUPPORTED_LOCALES" :key="locale.code" :value="locale.code">
          <div class="flex items-center space-x-2">
            <span>{{ locale.flag }}</span>
            <span>{{ locale.name }}</span>
          </div>
        </SelectItem>
      </SelectContent>
    </Select>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from '@mattverse/i18n'
import { SUPPORTED_LOCALES, type SupportedLocale } from '@mattverse/i18n'
import { useSettingsStore } from '@/store'
import { logger } from '@mattverse/shared'

const { locale } = useI18n()
const settingsStore = useSettingsStore()

const currentLanguage = computed(() => settingsStore.language)

const handleLanguageChange = (newLanguage: SupportedLocale) => {
  // 更新设置状态
  settingsStore.setLanguage(newLanguage)

  // 更新i18n实例的语言
  locale.value = newLanguage

  // 强制重新渲染以确保翻译更新
  nextTick(() => {
    logger.info(`语言已切换为 ${newLanguage}`)
  })
}
</script>
