<template>
  <Card class="break-inside-avoid bg-card/70 backdrop-blur-md border-white/20 hover:shadow-lg hover:bg-card/80 transition-all duration-300 hover:-translate-y-1">
    <CardHeader>
      <CardTitle class="flex items-center space-x-2">
        <MattIcon name="Activity" class="h-5 w-5" />
        <span>{{ $t('settings.middleware.connection_status') }}</span>
      </CardTitle>
    </CardHeader>
    <CardContent class="space-y-4">
      <!-- 连接状态 -->
      <div class="flex justify-between items-center py-2 border-b border-border/50">
        <span class="text-sm text-muted-foreground">{{ $t('settings.middleware.status') }}</span>
        <div class="flex items-center space-x-2">
          <div 
            :class="[
              'w-2 h-2 rounded-full',
              connectionStatus === 'READY' ? 'bg-green-500' : 
              connectionStatus === 'CONNECTING' ? 'bg-yellow-500' : 
              'bg-red-500'
            ]"
          ></div>
          <span class="text-sm font-medium">{{ getStatusText(connectionStatus) }}</span>
        </div>
      </div>

      <!-- 当前服务地址 -->
      <div class="flex justify-between items-center py-2 border-b border-border/50">
        <span class="text-sm text-muted-foreground">{{ $t('settings.middleware.current_address') }}</span>
        <span class="text-sm font-medium">{{ currentAddress }}</span>
      </div>

      <!-- 中台版本号 -->
      <div class="flex justify-between items-center py-2">
        <span class="text-sm text-muted-foreground">{{ $t('settings.middleware.version') }}</span>
        <span class="text-sm font-medium">{{ middlewareVersion || '--' }}</span>
      </div>
    </CardContent>
  </Card>
</template>

<script setup lang="ts">
import { useI18n } from '@mattverse/i18n'
import type { ConnectionState } from '@/store'

interface Props {
  connectionStatus: ConnectionState
  currentAddress: string
  middlewareVersion: string
}

defineProps<Props>()

// 国际化
const { t } = useI18n()

// 获取状态文本
const getStatusText = (status: ConnectionState): string => {
  const statusMap = {
    'IDLE': t('settings.middleware.status_idle'),
    'CONNECTING': t('settings.middleware.status_connecting'),
    'READY': t('settings.middleware.status_ready'),
    'TRANSIENT_FAILURE': t('settings.middleware.status_failed'),
    'SHUTDOWN': t('settings.middleware.status_shutdown')
  }
  return statusMap[status] || status
}
</script>
