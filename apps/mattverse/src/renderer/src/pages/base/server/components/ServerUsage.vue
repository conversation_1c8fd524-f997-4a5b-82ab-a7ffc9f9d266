<template>
  <ScrollArea class="h-[45vh] mb-8 w-full">
    <div class="flex flex-col justify-center items-center">
      <div class="flex justify-between items-center px-4 pt-2">
        <h1 class="text-xl font-bold text-foreground text-[#222222]">
          {{ showData.serverName }} 服务器
        </h1>
      </div>
    </div>
    <!-- 服务器用量表 -->
    <div class="w-full">
      <div class="flex justify-between items-center px-4 pt-2">
        <h1 class="text-base font-bold text-foreground text-[#3d3d3d]">资源用量</h1>
      </div>
      <table class="mt-2 w-full">
        <thead>
          <tr class="border-t border-b bg-muted/50">
            <!-- 表头 -->
            <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">
              服务器名称
            </th>
            <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">使用内存</th>
            <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">空闲内存</th>
            <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">总内存</th>
            <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">
              内存使用率
            </th>
            <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">cpu使用率</th>
          </tr>
        </thead>
        <tbody>
          <tr class="border-b">
            <td class="py-2 px-4 text-left text-sm font-medium">
              {{ showData.serverName }}
            </td>
            <td class="py-2 px-4 text-left text-sm font-medium">
              {{ showData.memoryStatus.usedMemory }}
            </td>
            <td class="py-2 px-4 text-left text-sm font-medium">
              {{ showData.memoryStatus.freeMemory }}
            </td>
            <td class="py-2 px-4 text-left text-sm font-medium">
              {{ showData.memoryStatus.totalMemory }}
            </td>
            <td class="py-2 px-4 text-left text-sm font-medium">
              <div class="flex flex-row items-center justify-center gap-1">
                <span class="w-2/3">
                  <Progress
                    v-model="showData.memoryStatus.memoryUsage"
                    class="w-full"
                    :style="{
                      '--progress-color': getProgressColor(showData.memoryStatus.memoryUsage),
                    }"
                  />
                </span>
                <span class="w-1/3 text-gray-500">{{ showData.memoryStatus.memoryUsage }}%</span>
              </div>
            </td>
            <td class="py-2 px-4 text-left text-sm font-medium">
              <div class="flex flex-row items-center justify-center gap-1">
                <span class="w-2/3">
                  <Progress
                    v-model="showData.cpuUsage"
                    class="w-full"
                    :style="{ '--progress-color': getProgressColor(showData.cpuUsage) }"
                  />
                </span>
                <span class="w-1/3 text-gray-500">{{ showData.cpuUsage }}%</span>
              </div>
            </td>
          </tr>
        </tbody>
      </table>

      <div>
        <div class="flex justify-between items-center px-4 pt-8">
          <h1 class="text-base font-bold text-foreground text-[#3d3d3d]">GPU用量</h1>
        </div>
        <div v-if="showData.gpuStatusList.length !== 0">
          <table class="mt-2 w-full">
            <thead>
              <tr class="border-t border-b bg-muted/50">
                <th class="w-16 py-2 px-4 text-left text-sm font-medium text-muted-foreground">
                  编号
                </th>
                <th class="w-48 py-2 px-4 text-left text-sm font-medium text-muted-foreground">
                  GPU名称
                </th>
                <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">
                  已使用显存
                </th>
                <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">
                  空闲显存
                </th>
                <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">
                  总显存
                </th>
                <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">
                  显存占用率
                </th>
                <th class="py-2 px-4 text-left text-sm font-medium text-muted-foreground">
                  显卡使用率
                </th>
              </tr>
            </thead>
            <tbody>
              <!-- 修复：为每个GPU项创建单独的tr，将td放在tr内部 -->
              <tr v-for="gpuItem in showData.gpuStatusList" :key="gpuItem.index" class="border-b">
                <td class="py-2 px-4 text-left text-sm font-medium">
                  {{ gpuItem.index }}
                </td>
                <td
                  v-tooltip="{
                    content: gpuItem.model,
                    placement: 'top',
                    theme: 'light',
                  }"
                  class="py-2 px-4 text-left text-sm font-medium"
                >
                  {{ gpuItem.model }}
                </td>
                <td class="py-2 px-4 text-left text-sm font-medium">
                  {{ gpuItem.usedMemory }}
                </td>
                <td class="py-2 px-4 text-left text-sm font-medium">
                  {{ gpuItem.freeMemory }}
                </td>
                <td class="py-2 px-4 text-left text-sm font-medium">
                  {{ gpuItem.totalMemory }}
                </td>
                <td class="py-2 px-4 text-left text-sm font-medium">
                  <div class="flex flex-row items-center justify-center gap-1">
                    <span class="w-2/3">
                      <Progress
                        v-model="gpuItem.memoryUsage"
                        class="w-full"
                        :style="{ '--progress-color': getProgressColor(gpuItem.memoryUsage) }"
                      />
                    </span>
                    <span class="w-1/3 text-gray-500">{{ gpuItem.memoryUsage }}%</span>
                  </div>
                </td>
                <td class="py-2 px-4 text-left text-sm font-medium">
                  <div class="flex flex-row items-center justify-center gap-1">
                    <span class="w-2/3">
                      <Progress
                        v-model="gpuItem.usage"
                        class="w-full"
                        :style="{ '--progress-color': getProgressColor(gpuItem.usage) }"
                      />
                    </span>
                    <span class="w-1/3 text-gray-500">{{ gpuItem.usage }}%</span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
          <div
            class="px-4 py-2 border-t flex justify-between items-center text-sm text-muted-foreground"
          >
            <span>共 {{ showData.gpuStatusList.length }} 条</span>
          </div>

          <!-- 底部老鼠 -->
          <div class="flex flex-col justify-center items-center pt-[10vh]">
            <div
              class="flex justify-center items-center px-4 py-2 border-t border-[rgba(136,136,136,0.1)]"
            >
              <MattIcon
                name="Rat"
                class="w-6 h-6 p-1 text-[#777777] shrink-0 hover:animate-rat-jump"
              />
              <h1 class="text-xs text-[#666666] text-opacity-50 cursor-default">已经到底了</h1>
            </div>
          </div>
        </div>
        <MattEmptyState
          v-else
          icon="Ban"
          title="暂无GPU数据"
          description="当前没有可显示的内容"
          :icon-size="35"
        >
        </MattEmptyState>
      </div>
    </div>
  </ScrollArea>
</template>
<script setup lang="ts">
import { useServerStore } from '@/store'
import { computed, onMounted, onUnmounted, ref } from 'vue'

const props = defineProps({
  serverId: {
    type: String,
    default: '',
  },
})

const serverStore = useServerStore()

let refreshInterval: number | null = null
const refreshTimer = ref<number>(2)
// 默认数据
const defaultData = {
  serverName: '',
  serverId: '',
  cpuUsage: 0,
  memoryStatus: {
    memoryUsage: 0,
    usedMemory: '',
    freeMemory: '',
    totalMemory: '',
    unit: '',
  },
  gpuStatusList: [],
}

const serverUsage = computed(() =>
  useServerStore().serverUsages.find(s => s.serverId === props.serverId)
)

// 展示数据
const showData = computed(() => {
  if (!serverUsage.value) return defaultData

  const memory = serverUsage.value.memoryStatus

  // 内存状态
  const memoryStatus = {
    memoryUsage: Number(formatDecimal((memory.usedMemory / memory.totalMemory) * 100, 1)),
    usedMemory: formatDecimal(memory.usedMemory) + ' ' + memory.unit,
    freeMemory: formatDecimal(memory.freeMemory) + ' ' + memory.unit,
    totalMemory: formatDecimal(memory.totalMemory) + ' ' + memory.unit,
    unit: memory.unit,
  }

  // gpu状态
  let gpuStatusList = serverUsage.value.gpuStatusList
  if (gpuStatusList && gpuStatusList.length !== 0) {
    gpuStatusList = gpuStatusList.map(item => ({
      index: item.index,
      model: item.model,
      usage: Number(formatDecimal(item.usage, 1)),
      // memoryUsage 百分号展示
      memoryUsage: Number(formatDecimal(item.memoryUsage * 100, 1)),
      usedMemory: formatDecimal(item.usedMemory) + ' ' + memory.unit,
      freeMemory: formatDecimal(item.freeMemory) + ' ' + memory.unit,
      totalMemory: formatDecimal(item.totalMemory) + ' ' + memory.unit,
    })) // 修复箭头函数括号闭合问题
  }

  // 返回显示数据
  return {
    serverName: serverUsage.value.serverName ?? defaultData.serverName,
    serverId: serverUsage.value.serverId ?? defaultData.serverId,
    cpuUsage: Number(formatDecimal(serverUsage.value.cpuUsage, 1)) ?? defaultData.cpuUsage,
    memoryStatus: memoryStatus ?? defaultData.memoryStatus,
    gpuStatusList: gpuStatusList ?? defaultData.gpuStatusList,
  }
})

// 格式化数字
const formatDecimal = (num: number | string, decimalPlaces = 2) => {
  if (typeof num === 'number') {
    return num.toFixed(decimalPlaces)
  }
  const number = parseFloat(num)
  if (Number.isNaN(number)) return num // 处理非数字
  return number.toFixed(decimalPlaces)
}

// 改变进度条颜色
const getProgressColor = (value: number) => {
  return value < 70 ? '#4ade80' : value < 90 ? '#facc15' : '#f87171'
}

// 刷新服务用量数据
const refreshServerUsage = async () => {
  if (serverStore.loadingUsage) return
  try {
    await serverStore.updateServerUsage(props.serverId)
  } catch (error) {
    console.error('刷新服务列表失败:', error)
  }
}

// 启动轮询
const startRefreshInterval = () => {
  if (refreshInterval !== null) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
  refreshInterval = window.setInterval(() => {
    refreshServerUsage()
  }, refreshTimer.value * 1000)
}

onMounted(() => {
  // 进入时刷新一下数据
  serverStore.updateServerUsage(props.serverId)
  // 启动轮询
  startRefreshInterval()
})

// 销毁轮询
onUnmounted(() => {
  if (refreshInterval !== null) {
    clearInterval(refreshInterval)
    refreshInterval = null
  }
})
</script>

<style scoped>
:deep(.bg-primary) {
  background-color: var(--progress-color) !important;
}

@keyframes rat-jump {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8px);
  }
}
.hover\:animate-rat-jump:hover {
  animation: rat-jump 0.3s ease-in-out;
}
</style>
