import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { useInterval } from '@vueuse/core'
import { createPersistConfig } from '@/store/plugins/persist-config'
import {logger,formatDuration, taskService, type Task, TaskStatus } from '@mattverse/shared'



// 定义轮询控制接口
interface PollingControl {
  stop: () => void
  resume: () => void
}
/**
 * 服务器状态管理
 */
export const useTaskStore = defineStore('task', () => {

  // 状态
  const tasks = ref<Task[]>([]) // 任务列表
  const loading = ref(false) // 加载状态
  const taskResult = ref<string | null>(null) // 任务结果
  const refreshInterval = ref<number>(5) // 任务列表刷新间隔（秒）
  const taskStatusMap = ref<Map<string, TaskStatus>>(new Map()) // 任务状态缓存
  const taskResultsMap = ref<Map<string, any>>(new Map()) // 任务结果缓存
  // 存储所有正在轮询的任务
  const pollingControls = ref<Map<string, PollingControl>>(new Map())

  // 获取特定任务
  const getTaskById = (taskId: string) =>
    computed(() => {
      return tasks.value.find((task) => task.taskId === taskId)
    })

  // 获取特定任务结果
  const getTaskResultById = (taskId: string) =>
    computed(() => {
      // 确保 Map 对象的正确性
      if (!(taskResultsMap.value instanceof Map)) {
        taskResultsMap.value = new Map(Object.entries(taskResultsMap.value || {}))
      }
      return taskResultsMap.value.get(taskId)
    })

  // 获取任务进度
  const getTaskProgress = (taskId: string) =>
    computed(() => {
      const task = tasks.value.find((t) => t.taskId === taskId)
      return task?.taskProcess || 0
    })

  // 获取任务状态
  const getTaskStatus = (taskId: string) =>
    computed(() => {
      // 确保 Map 对象的正确性
      if (!(taskStatusMap.value instanceof Map)) {
        taskStatusMap.value = new Map(Object.entries(taskStatusMap.value || {}))
      }
      return taskStatusMap.value.get(taskId) || 'Unknown'
    })

  // 获取状态显示文本
  const getStatusText = (status: TaskStatus): string => {
    const statusText: Record<TaskStatus, string> = {
      Initializing: '初始化中',
      Computing: '计算中',
      Pending: '等待调度',
      Paused: '暂停',
      Finished: '任务完成',
      Error: '任务失败',
      TaskStay: '原始状态',
      Abort: '已终止',
    }
    return statusText[status] || status
  }

  // 获取状态对应的样式
  const getStatusClass = (status: TaskStatus): string => {
    const classes: Record<TaskStatus, string> = {
      Initializing: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
      Computing: 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300',
      Pending: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300',
      Paused: 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300',
      Finished: 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300',
      Error: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
      TaskStay: 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-300',
      Abort: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300',
    }
    return classes[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300'
  }

  // 解析任务结果
  const parseTaskResult = (result: string): any => {
    try {
      return JSON.parse(result)
    } catch (e) {
      logger.error('解析任务结果失败:', e)
      return null
    }
  }

  // 更新任务结果
  const updateResult = (taskId: string, result: string) => {
    const taskIndex = tasks.value.findIndex((t) => t.taskId === taskId)
    if (taskIndex !== -1) {
      tasks.value[taskIndex].result = result
    }
    const parsedResult = parseTaskResult(result)
    if (parsedResult) {
      // 确保 Map 对象的正确性
      if (!(taskResultsMap.value instanceof Map)) {
        taskResultsMap.value = new Map(Object.entries(taskResultsMap.value || {}))
      }
      taskResultsMap.value.set(taskId, parsedResult)
    }
  }

  // 更新任务列表
  const updateTaskList = async (id?: string) => {
    loading.value = true
    try {
      const res = await taskService.getTaskList(id)
      if (res.status === 'Success' && res.taskResult?.taskList) {
        const newTasks = res.taskResult.taskList

        // 直接使用服务端返回的任务列表，确保数据的实时性
        newTasks.forEach((task) => {
          task.duration = formatDuration(task.startTime, task.endTime)
          // 确保 Map 对象的正确性
          if (!(taskStatusMap.value instanceof Map)) {
            taskStatusMap.value = new Map(Object.entries(taskStatusMap.value || {}))
          }
          // 更新状态缓存，用于轮询和状态判断
          taskStatusMap.value.set(task.taskId, task.taskStatus)
        })

        // 清理已删除任务的缓存数据
        const newTaskIds = new Set(newTasks.map(t => t.taskId))

        // 确保 Map 对象的正确性
        if (!(taskStatusMap.value instanceof Map)) {
          taskStatusMap.value = new Map(Object.entries(taskStatusMap.value || {}))
        }
        if (!(taskResultsMap.value instanceof Map)) {
          taskResultsMap.value = new Map(Object.entries(taskResultsMap.value || {}))
        }

        for (const [taskId] of taskStatusMap.value.entries()) {
          if (!newTaskIds.has(taskId)) {
            taskStatusMap.value.delete(taskId)
            taskResultsMap.value.delete(taskId)
            // 停止已删除任务的轮询
            stopPolling(taskId)
          }
        }

        // 直接替换任务列表，不进行合并
        tasks.value = newTasks
      }
    } finally {
      loading.value = false
    }
  }

  // 获取任务结果
  const updateTaskResult = async (id: string) => {
    const res = await taskService.getTaskResult(id)
    if (res.status !== 'Success' || !res.result) {
      return null
    }

    // 处理批量请求
    if (id.includes(',')) {
      const taskIds = id.split(',')
      const results = res.result.split('},{').map((str, index) => {
        if (index === 0) return str + '}'
        if (index === res.result.split('},{').length - 1) return '{' + str
        return '{' + str + '}'
      })

      // 确保结果数量与任务ID数量匹配
      if (results.length === taskIds.length) {
        taskIds.forEach((taskId, index) => {
          const result = results[index]
          if (result) {
            updateResult(taskId, result)
          }
        })
      }
    } else {
      // 处理单个任务
      updateResult(id, res.result)
    }
    return res.result
  }

  // 启动轮询
  const startPolling = (taskId: string) => {
    stopPolling(taskId)
    const { pause, resume } = useInterval(refreshInterval.value * 1000, {
      immediate: true,
      controls: true,
      callback: async () => {
        try {
          await updateTaskList(taskId)
          // 确保 Map 对象的正确性
          if (!(taskStatusMap.value instanceof Map)) {
            taskStatusMap.value = new Map(Object.entries(taskStatusMap.value || {}))
          }
          const status = taskStatusMap.value.get(taskId)
          if (['Computing', 'Pending', 'Initializing'].includes(status as TaskStatus)) {
            await updateTaskResult(taskId)
          }
          if (['Finished', 'Error', 'Abort', 'Paused'].includes(status as TaskStatus)) {
            await updateTaskResult(taskId)
            stopPolling(taskId)
          }
        } catch (error) {
          logger.error(`轮询任务 ${taskId} 出错:`, error)
        }
      },
    })
    // 确保 Map 对象的正确性
    if (!(pollingControls.value instanceof Map)) {
      pollingControls.value = new Map(Object.entries(pollingControls.value || {}))
    }
    pollingControls.value.set(taskId, { stop: pause, resume })
    resume()
  }

  // 停止轮询
  const stopPolling = (taskId: string) => {
    // 确保 Map 对象的正确性
    if (!(pollingControls.value instanceof Map)) {
      pollingControls.value = new Map(Object.entries(pollingControls.value || {}))
    }
    const polling = pollingControls.value.get(taskId)
    if (polling) {
      polling.stop()
      pollingControls.value.delete(taskId)
    }
  }

  // 停止所有轮询
  const stopAllPolling = () => {
    // 确保 Map 对象的正确性
    if (!(pollingControls.value instanceof Map)) {
      pollingControls.value = new Map(Object.entries(pollingControls.value || {}))
    }
    for (const [taskId, polling] of pollingControls.value.entries()) {
      polling.stop()
      pollingControls.value.delete(taskId)
    }
  }

  // 设置刷新间隔
  const setRefreshInterval = (interval: number) => {
    refreshInterval.value = interval
  }

  // 清除任务结果
  const clearTaskResult = (taskId: string) => {
    stopPolling(taskId)

    // 确保 Map 对象的正确性
    if (!(taskResultsMap.value instanceof Map)) {
      taskResultsMap.value = new Map(Object.entries(taskResultsMap.value || {}))
    }
    if (!(taskStatusMap.value instanceof Map)) {
      taskStatusMap.value = new Map(Object.entries(taskStatusMap.value || {}))
    }

    taskResultsMap.value.delete(taskId)
    taskStatusMap.value.delete(taskId)
    tasks.value = tasks.value.filter((task) => task.taskId !== taskId)
  }

  // 清除所有任务结果
  const clearAllTaskResults = () => {
    stopAllPolling()

    // 确保 Map 对象的正确性
    if (!(taskResultsMap.value instanceof Map)) {
      taskResultsMap.value = new Map()
    } else {
      taskResultsMap.value.clear()
    }

    if (!(taskStatusMap.value instanceof Map)) {
      taskStatusMap.value = new Map()
    } else {
      taskStatusMap.value.clear()
    }

    tasks.value = []
    taskResult.value = null
  }

  // 判断任务是否正在轮询
  const isPolling = (taskId: string): boolean => {
    // 确保 Map 对象的正确性
    if (!(pollingControls.value instanceof Map)) {
      pollingControls.value = new Map(Object.entries(pollingControls.value || {}))
    }
    return pollingControls.value.has(taskId)
  }

  return {
    tasks,
    loading,
    taskResult,
    taskResultsMap,
    refreshInterval,
    getTaskById,
    getTaskResultById,
    getTaskProgress,
    getTaskStatus,
    getStatusText,
    getStatusClass,
    updateTaskList,
    updateTaskResult,
    startPolling,
    stopPolling,
    stopAllPolling,
    setRefreshInterval,
    clearTaskResult,
    clearAllTaskResults,
    isPolling,
  }
},
  {
    persist: createPersistConfig('task'),
  })

