import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { nodeModules, type NodeModule } from '@mattverse/shared'
import { createPersistConfig } from '@/store/plugins/persist-config'

type ToolModuleGroup = {
  name: string
  icon?: { type: string; value: string }
  enabled?: boolean
  isBuiltin?: boolean
  type: string
  description?: string
}

export const useNodeToolsStore = defineStore(
  'nodeModules',
  () => {
    // 存储所有节点模块，初始化时合并默认模块和持久化数据
    const nodeModulesData = ref<Record<string, NodeModule>>({})

    return {}
  },
  {
    persist: createPersistConfig('nodeTools'),
  }
)
