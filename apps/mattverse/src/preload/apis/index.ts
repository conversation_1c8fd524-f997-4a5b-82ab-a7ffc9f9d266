/**
 * 预加载进程 API 统一导出
 */
import { systemAPI } from './system'
import { appAPI } from './app'
import { middlewareAPI } from './middleware'
import { grpcAPI } from './grpc'
import { cacheAPI } from './cache'
import { loggerAPI } from '@mattverse/electron-core'
import { dialogAPI } from './dialog'

/**
 * 合并所有 API 模块
 */
export const customAPI = {
  // 系统信息
  ...systemAPI,

  // Mattverse 应用配置
  ...appAPI,

  // 中台配置相关 API
  middleware: middlewareAPI,

  // gRPC 相关 API
  grpc: grpcAPI,

  // 缓存管理 API
  cache: cacheAPI,

  // 日志管理 API
  logger: loggerAPI,

  // 对话框 API
  dialog: dialogAPI,
}

/**
 * 导出各个模块的 API（如果需要单独使用）
 */
export { systemAPI, appAPI, middlewareAPI, grpcAPI, cacheAPI, dialogAPI }
export { loggerAPI } from '@mattverse/electron-core'
