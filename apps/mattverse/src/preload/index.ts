/**
 * Mattverse 预加载脚本
 */
import { contextBridge } from 'electron'
import { createPreloadBridge, logger } from '@mattverse/electron-core'
import { customAPI } from './apis'

// 使用 electron-core 的预加载桥接工厂
const api = createPreloadBridge(customAPI)

// 暴露 API 到渲染进程
if (process.contextIsolated) {
  try {
    contextBridge.exposeInMainWorld('electronAPI', api)
    logger.info('Mattverse APIs exposed successfully')
  } catch (error) {
    logger.error('Failed to expose Mattverse APIs:', error)
  }
} else {
  // @ts-ignore (define in dts)
  window.electronAPI = api
}

// 导出类型定义
export type ElectronAPI = typeof api
