export default {
  // 基础格式化选项
  semi: false,
  singleQuote: true,
  tabWidth: 2,
  useTabs: false,
  trailingComma: 'es5',
  printWidth: 100,
  endOfLine: 'lf',

  // 括号和空格
  bracketSpacing: true,
  bracketSameLine: false,
  arrowParens: 'avoid',

  // HTML/Vue 特定设置
  htmlWhitespaceSensitivity: 'css',
  vueIndentScriptAndStyle: false,

  // 引号设置
  quoteProps: 'as-needed',
  jsxSingleQuote: true,

  // 换行设置
  proseWrap: 'preserve',

  // 文件覆盖设置
  overrides: [
    {
      files: '*.vue',
      options: {
        parser: 'vue'
      }
    },
    {
      files: '*.json',
      options: {
        parser: 'json',
        trailingComma: 'none'
      }
    },
    {
      files: '*.md',
      options: {
        parser: 'markdown',
        printWidth: 80,
        proseWrap: 'always'
      }
    },
    {
      files: '*.{yml,yaml}',
      options: {
        parser: 'yaml',
        tabWidth: 2
      }
    }
  ]
}
