import { nextTick } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import { logger } from '../utils'

// 检查图表实例是否有效
const isValidChart = (chart) => {
  if (
    !chart ||
    typeof chart.resize !== 'function' ||
    typeof chart.getDom !== 'function' ||
    !chart.getDom()
  ) {
    // window.logger.info('Invalid chart: missing required methods or DOM')
    return false
  }
  // 检查图表是否已销毁
  if (chart.isDisposed && typeof chart.isDisposed === 'function' && chart.isDisposed()) {
    logger.info('Invalid chart: already disposed')
    return false
  }
  // 检查 option 和 series
  try {
    const option = chart.getOption()
    if (!option || !option.series || option.series.length === 0) {
      // window.logger.info('Invalid chart: missing or empty series', option)
      return false
    }
    // 确保 series 数据有效
    for (const series of option.series) {
      if (!series.type || !Array.isArray(series.data) || series.data.length === 0) {
        // window.logger.info('Invalid chart: invalid series data', series)
        return false
      }
    }
  } catch (e) {
    // window.logger.info('Invalid chart option:', e)
    return false
  }
  // 检查容器尺寸
  const dom = chart.getDom()
  if (!dom.offsetWidth || !dom.offsetHeight) {
    // window.logger.info('Invalid container size:', {
    //   width: dom.offsetWidth,
    //   height: dom.offsetHeight,
    // })
    return false
  }
  return true
}

const tryResize = (chart) => {
  if (!isValidChart(chart)) {
    // window.logger.info('Skipping resize for invalid chart')
    return
  }

  try {
    // window.logger.info('Resizing chart, container size:', {
    //   width: chart.getDom().offsetWidth,
    //   height: chart.getDom().offsetHeight,
    // })
    chart.resize()
  } catch (e) {
    logger.error('调整图表大小失败:', e)
  }
}

/**
 * ECharts 图表自适应大小指令
 * 使用方法：v-chart-resize="chartInstance"
 */
export const vChartResize = {
  mounted(el, binding) {
    const chart = binding.value
    if (!isValidChart(chart)) return

    // 防抖处理 ResizeObserver 回调
    const handleResize = useDebounceFn(() => {
      window.requestAnimationFrame(() => {
        tryResize(chart)
      })
    }, 200)

    const resizeObserver = new ResizeObserver(handleResize)
    resizeObserver.observe(el)

    // 防抖处理窗口 resize 事件
    const handleWindowResize = useDebounceFn(() => {
      window.requestAnimationFrame(() => {
        tryResize(chart)
      })
    }, 200)
    window.addEventListener('resize', handleWindowResize)

    // 保存清理函数
    el._chartResizeCleanup = () => {
      resizeObserver.disconnect()
      window.removeEventListener('resize', handleWindowResize)
    }
  },
  updated(el, binding) {
    // 延迟更新以确保图表实例就绪
    nextTick(() => {
      // 清理旧的监听器
      if (el._chartResizeCleanup) {
        el._chartResizeCleanup()
      }

      const chart = binding.value
      if (!isValidChart(chart)) return

      // 防抖处理 ResizeObserver 回调
      const handleResize = useDebounceFn(() => {
        window.requestAnimationFrame(() => {
          tryResize(chart)
        })
      }, 200)

      const resizeObserver = new ResizeObserver(handleResize)
      resizeObserver.observe(el)

      // 防抖处理窗口 resize 事件
      const handleWindowResize = useDebounceFn(() => {
        window.requestAnimationFrame(() => {
          tryResize(chart)
        })
      }, 200)
      window.addEventListener('resize', handleWindowResize)

      // 更新清理函数
      el._chartResizeCleanup = () => {
        resizeObserver.disconnect()
        window.removeEventListener('resize', handleWindowResize)
      }
    })
  },
  unmounted(el) {
    if (el._chartResizeCleanup) {
      el._chartResizeCleanup()
      delete el._chartResizeCleanup
    }
  },
}

/**
 * 支持多图表实例的指令（保留以备后用）
 * 使用方法：v-chart-resize-multi="{ positive: positiveChartInstance, negative: negativeChartInstance }"
 */
export const vChartResizeMulti = {
  mounted(el, binding) {
    const chartInstances = binding.value
    if (!chartInstances || typeof chartInstances !== 'object') {
      // window.logger.info('Invalid chart instances:', chartInstances)
      return
    }

    const handleResize = useDebounceFn(() => {
      window.requestAnimationFrame(() => {
        // window.logger.info('Resize triggered for charts:', chartInstances)
        Object.values(chartInstances).forEach((chart) => {
          tryResize(chart)
        })
      })
    }, 200)

    const resizeObserver = new ResizeObserver(handleResize)
    resizeObserver.observe(el)

    const handleWindowResize = useDebounceFn(() => {
      window.requestAnimationFrame(() => {
        Object.values(chartInstances).forEach((chart) => {
          tryResize(chart)
        })
      })
    }, 200)
    window.addEventListener('resize', handleWindowResize)

    el._chartResizeCleanup = () => {
      resizeObserver.disconnect()
      window.removeEventListener('resize', handleWindowResize)
    }
  },
  updated(el, binding) {
    nextTick(() => {
      if (el._chartResizeCleanup) {
        el._chartResizeCleanup()
      }

      const chartInstances = binding.value
      if (!chartInstances || typeof chartInstances !== 'object') {
        logger.info('Invalid chart instances:', chartInstances)
        return
      }

      const handleResize = useDebounceFn(() => {
        window.requestAnimationFrame(() => {
          // window.logger.info('Resize triggered for charts:', chartInstances)
          Object.values(chartInstances).forEach((chart) => {
            tryResize(chart)
          })
        })
      }, 200)

      const resizeObserver = new ResizeObserver(handleResize)
      resizeObserver.observe(el)

      const handleWindowResize = useDebounceFn(() => {
        window.requestAnimationFrame(() => {
          Object.values(chartInstances).forEach((chart) => {
            tryResize(chart)
          })
        })
      }, 200)
      window.addEventListener('resize', handleWindowResize)

      el._chartResizeCleanup = () => {
        resizeObserver.disconnect()
        window.removeEventListener('resize', handleWindowResize)
      }
    })
  },
  unmounted(el) {
    if (el._chartResizeCleanup) {
      el._chartResizeCleanup()
      delete el._chartResizeCleanup
    }
  },
}

