// 材料设计
export { default as materialCalculation } from '../assets/svg/materialDesign/materialCalculation.svg'
export { default as structure } from '../assets/svg/materialDesign/structure.svg'
export { default as electrode } from '../assets/svg/materialDesign/electrode.svg'
export { default as electrolyte } from '../assets/svg/materialDesign/electrolyte.svg'
export { default as energy } from '../assets/svg/materialDesign/energy.svg'
export { default as spring } from '../assets/svg/materialDesign/spring.svg'
export { default as diffusion } from '../assets/svg/materialDesign/diffusion.svg'
export { default as conductivity } from '../assets/svg/materialDesign/conductivity.svg'
export { default as viscosity } from '../assets/svg/materialDesign/viscosity.svg'
export { default as solvatedStructure } from '../assets/svg/materialDesign/solvatedStructure.svg'
export { default as phaseDiagram } from '../assets/svg/materialDesign/phaseDiagram.svg'
export { default as md } from '../assets/svg/materialDesign/md.svg'

// 电池模拟
export { default as batteryModel } from '../assets/svg/batterySimulation/batteryModel.svg'
export { default as lifePrediction } from '../assets/svg/batterySimulation/lifePrediction.svg'
export { default as batteryDataInput } from '../assets/svg/batterySimulation/batteryDataInput.svg'
export { default as batteryCalibration } from '../assets/svg/batterySimulation/batteryCalibration.svg'
export { default as batterySimulation } from '../assets/svg/batterySimulation/batterySimulation.svg'
export { default as anode } from '../assets/svg/batterySimulation/anode.svg'
export { default as cathode } from '../assets/svg/batterySimulation/cathode.svg'
export { default as separator } from '../assets/svg/batterySimulation/separator.svg'
export { default as BElectrolyte } from '../assets/svg/batterySimulation/BElectrolyte.svg'
export { default as simulationCalculation } from '../assets/svg/batterySimulation/simulationCalculation.svg'
export { default as lifespanPrediction } from '../assets/svg/batterySimulation/lifespanPrediction.svg'
export { default as dataBase } from '../assets/svg/batterySimulation/dataBase.svg'
export { default as featureExtraction } from '../assets/svg/batterySimulation/featureExtraction.svg'

// 工艺优化
export { default as processOptimization } from '../assets/svg/processOptimization/processOptimization.svg'
export { default as cathodeFormulation } from '../assets/svg/processOptimization/cathodeFormulation.svg'
export { default as anodeFormulation } from '../assets/svg/processOptimization/anodeFormulation.svg'
export { default as homogenizationProcess } from '../assets/svg/processOptimization/homogenizationProcess.svg'
export { default as dryingProcess } from '../assets/svg/processOptimization/dryingProcess.svg'
export { default as rollingProcess } from '../assets/svg/processOptimization/rollingProcess.svg'
export { default as densityCalculation } from '../assets/svg/processOptimization/densityCalculation.svg'
export { default as slurryViscosity } from '../assets/svg/processOptimization/slurryViscosity.svg'
export { default as porosity } from '../assets/svg/processOptimization/porosity.svg'
export { default as numeric } from '../assets/svg/processOptimization/numeric.svg'
export { default as threeDStructure } from '../assets/svg/processOptimization/threeDStructure.svg'
export { default as coordinateData } from '../assets/svg/processOptimization/coordinateData.svg'
export { default as lineChart } from '../assets/svg/processOptimization/lineChart.svg'

// 其他
export { default as splice } from '../assets/svg/splice.svg'
export { default as output } from '../assets/svg/output.svg'
export { default as condition } from '../assets/svg/condition.svg'
export { default as sequence } from '../assets/svg/sequence.svg'
export { default as mysql } from '../assets/svg/mysql.svg'

// 语言图标
export { default as zhCn } from '../assets/svg/iconfont/zh-cn.svg'
export { default as enUs } from '../assets/svg/iconfont/en-us.svg'
export { default as jaJp } from '../assets/svg/iconfont/jp.svg'
export { default as koKr } from '../assets/svg/iconfont/ko-kr.svg'
