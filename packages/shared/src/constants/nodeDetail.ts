import * as svg from './svg'
import { nanoid } from 'nanoid'
import { NodeModule } from '../types/nodeDetail'

const nodeDetail: Record<string, NodeModule> = {
  豪鹏电池寿命预测: {
    description: '豪鹏电池寿命预测模块',
    icon: { type: 'icon', value: 'material-symbols:battery-charging-full-sharp' },

    type: 'HighpowerLifePrediction',
    enabled: true,
    isBuiltin: true,
    name: '豪鹏电池寿命预测',
    categories: [
      {
        name: '基础组件',
        nodes: [
          {
            data: {
              category: 'materialDesign',
              description: '',
              icon: {
                type: 'svg',
                value: svg.batteryDataInput,
              },
              inputType: [],
              label: '豪鹏电芯数据输入',
              nodeType: 'Basic',
              outputType: ['HighpowerBatteryModelCalibration'],
              params: {},
              type: 'HighpowerCellDataInput',
            },
            id: '1',
            type: 'custom',
          },
          {
            data: {
              category: 'materialDesign',
              description: '',
              icon: {
                type: 'svg',
                value: svg.batteryModel,
              },
              label: '豪鹏电池模型',
              nodeType: 'Basic',
              inputType: ['HighpowerBatteryModelCalibration'],
              outputType: ['HighpowerCellLifePrediction'],
              params: {},
              type: 'HighpowerBatteryModel',
            },
            id: '2',
            type: 'custom',
          },
        ],
      },
      {
        name: '计算组件',
        nodes: [
          {
            data: {
              category: 'materialDesign',
              description: '',
              icon: {
                type: 'svg',
                value: svg.batteryCalibration,
              },
              label: '豪鹏电池模型标定',
              nodeType: 'Compute',
              inputType: ['HighpowerCellDataInput'],
              outputType: ['HighpowerBatteryModel'],
              params: {},
              type: 'HighpowerBatteryModelCalibration',
            },
            id: '3',
            type: 'custom',
          },
          {
            data: {
              category: 'materialDesign',
              description: '',
              icon: {
                type: 'svg',
                value: svg.lifePrediction,
              },
              label: '豪鹏电芯寿命预测',
              nodeType: 'Compute',
              inputType: ['HighpowerBatteryModel'],
              outputType: [],
              params: {},
              type: 'HighpowerCellLifePrediction',
            },
            id: '4',
            type: 'custom',
          },
        ],
      },
    ],
  },
  材料设计: {
    icon: { type: 'svg', value: svg.materialCalculation },
    enabled: true,
    isBuiltin: true,
    name: '材料设计',
    type: 'materialCalculation',
    description: '材料设计模块',
    categories: [
      {
        name: '基础组件',
        nodes: [
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '电极',
              type: 'Electrode',
              icon: { type: 'svg', value: svg.structure },
              description: '',
              category: 'materialDesign',
              nodeType: 'Basic',
              inputType: [],
              outputType: ['Energy', 'Modulus', 'Diffusion', 'Conductivity', 'PhaseDiagram', 'MD'],
              params: {
                material: '',
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '电解液',
              type: 'Electrolyte',
              icon: { type: 'svg', value: svg.electrolyte },
              description: '',
              category: 'materialDesign',
              nodeType: 'Basic',
              inputType: [],
              outputType: ['Energy', 'Modulus', 'Diffusion', 'Conductivity', 'PhaseDiagram', 'MD'],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
        ],
      },
      {
        name: '计算组件',
        nodes: [
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '能量',
              type: 'Energy',
              icon: { type: 'svg', value: svg.energy },
              description: '',
              category: 'materialDesign',
              nodeType: 'Compute',
              inputType: ['Electrode'],
              outputType: [],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '模量',
              type: 'Modulus',
              icon: { type: 'svg', value: svg.spring },
              description: '',
              category: 'materialDesign',
              nodeType: 'Compute',
              inputType: ['Electrode'],
              outputType: ['Diffusion', 'Conductivity'],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '扩散系数',
              type: 'Diffusion',
              icon: { type: 'svg', value: svg.diffusion },
              description: '',
              category: 'materialDesign',
              nodeType: 'Compute',
              inputType: ['Electrode', 'Electrolyte'],
              outputType: ['Cathode', 'Anode', 'Separator', 'Electrolyte'],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '电导率',
              type: 'Conductivity',
              icon: { type: 'svg', value: svg.conductivity },
              description: '',
              category: 'materialDesign',
              nodeType: 'Compute',
              inputType: ['Electrode', 'Electrolyte'],
              outputType: ['Cathode', 'Anode', 'Separator', 'Electrolyte', 'Electrode'],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '黏度',
              type: 'Viscosity',
              icon: { type: 'svg', value: svg.viscosity },
              description: '',
              category: 'materialDesign',
              nodeType: 'Compute',
              inputType: ['Electrolyte'],
              outputType: ['Electrolyte'],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '溶剂化结构',
              type: 'SolvationStructure',
              icon: { type: 'svg', value: svg.solvatedStructure },
              description: '',
              category: 'materialDesign',
              nodeType: 'Compute',
              inputType: ['Electrolyte'],
              outputType: ['Electrolyte'],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '相图',
              type: 'PhaseDiagram',
              icon: { type: 'svg', value: svg.phaseDiagram },
              description: '',
              category: 'materialDesign',
              nodeType: 'Compute',
              inputType: ['Electrode'],
              outputType: [],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '自定义计算',
              type: 'materialDesign',
              icon: { type: 'svg', value: svg.md },
              description: '',
              category: 'materialDesign',
              nodeType: 'Compute',
              inputType: ['Electrode'],
              outputType: [],
              params: {
                layers: [],
                thickness: 0,
              },
            },
          },
        ],
      },
    ],
  },
  电池模拟: {
    icon: { type: 'svg', value: svg.batterySimulation },
    enabled: true,
    isBuiltin: true,
    name: '电池模拟',
    type: 'batterySimulation',
    description: '电池模拟模块',
    categories: [
      {
        name: '基础组件',
        nodes: [
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '正极',
              type: 'Cathode',
              icon: { type: 'svg', value: svg.anode },
              description: '',
              category: 'batterySimulation',
              nodeType: 'Basic',
              inputType: ['Conductivity', 'Diffusion'],
              outputType: ['Geometry'],
              params: {
                material: '',
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '负极',
              type: 'Anode',
              icon: { type: 'svg', value: svg.cathode },
              description: '',
              category: 'batterySimulation',
              nodeType: 'Basic',
              inputType: ['Conductivity', 'Diffusion'],
              outputType: ['Geometry'],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '隔膜',
              type: 'Separator',
              icon: { type: 'svg', value: svg.separator },
              description: '',
              category: 'batterySimulation',
              nodeType: 'Basic',
              inputType: [],
              outputType: ['Geometry'],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '电解液',
              type: 'Electrolyte',
              icon: { type: 'svg', value: svg.BElectrolyte },
              description: '',
              category: 'batterySimulation',
              nodeType: 'Basic',
              inputType: [],
              outputType: ['Geometry'],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '电芯模型',
              type: 'Geometry',
              icon: { type: 'svg', value: svg.splice },
              description: '',
              category: 'batterySimulation',
              nodeType: 'Basic',
              inputType: ['Cathode', 'Anode', 'Separator', 'Electrolyte'],
              outputType: ['Simulation'],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
        ],
      },
      {
        name: '计算组件',
        nodes: [
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '仿真计算',
              type: 'SimulationCalculation',
              icon: { type: 'svg', value: svg.simulationCalculation },
              description: '',
              category: 'batterySimulation',
              nodeType: 'Compute',
              inputType: ['Geometry'],
              outputType: [],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '寿命预测',
              type: 'LifespanPrediction',
              icon: { type: 'svg', value: svg.lifespanPrediction },
              description: '',
              category: 'batterySimulation',
              nodeType: 'Compute',
              inputType: ['FeatureExtraction'],
              outputType: [],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
        ],
      },
      {
        name: '数据组件',
        nodes: [
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '数据库',
              type: 'DataBase',
              icon: { type: 'svg', value: svg.dataBase },
              description: '',
              category: 'batterySimulation',
              nodeType: 'Data',
              inputType: [],
              outputType: ['FeatureExtraction'],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '特征提取',
              type: 'featureExtraction',
              icon: { type: 'svg', value: svg.featureExtraction },
              description: '',
              category: 'batterySimulation',
              nodeType: 'Data',
              inputType: ['DataBase'],
              outputType: ['LifespanPrediction'],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
        ],
      },
    ],
  },
  工艺优化: {
    icon: { type: 'svg', value: svg.processOptimization },
    enabled: false,
    isBuiltin: true,
    name: '工艺优化',
    type: 'processOptimization',
    description: '电池管理模块',
    categories: [
      {
        name: '基础组件',
        nodes: [
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '正极配方',
              type: 'cathodeFormulation',
              icon: { type: 'svg', value: svg.cathodeFormulation },
              description: '',
              category: 'processOptimization',
              nodeType: 'Basic',
              inputType: [],
              outputType: [],
              params: {
                material: '',
                thickness: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '负极配方',
              type: 'anodeFormulation',
              icon: { type: 'svg', value: svg.anodeFormulation },
              description: '',
              category: 'processOptimization',
              nodeType: 'Basic',
              inputType: [],
              outputType: [],
              params: {
                material: '',
                thickness: 0,
              },
            },
          },
        ],
      },
      {
        name: '计算组件',
        nodes: [
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '匀浆过程',
              type: 'homogenizationProcess',
              icon: { type: 'svg', value: svg.homogenizationProcess },
              description: '',
              category: 'processOptimization',
              nodeType: 'Compute',
              inputType: [],
              outputType: [],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '干燥过程',
              type: 'dryingProcess',
              icon: { type: 'svg', value: svg.dryingProcess },
              description: '',
              category: 'processOptimization',
              nodeType: 'Compute',
              inputType: [],
              outputType: [],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '辊压过程',
              type: 'rollingProcess',
              icon: { type: 'svg', value: svg.rollingProcess },
              description: '',
              category: 'processOptimization',
              nodeType: 'Compute',
              inputType: [],
              outputType: [],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '密度计算',
              type: 'densityCalculation',
              icon: { type: 'svg', value: svg.densityCalculation },
              description: '',
              category: 'processOptimization',
              nodeType: 'Compute',
              inputType: [],
              outputType: [],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '浆料黏度',
              type: 'slurryViscosity',
              icon: { type: 'svg', value: svg.slurryViscosity },
              description: '',
              category: 'processOptimization',
              nodeType: 'Compute',
              inputType: [],
              outputType: [],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '孔隙率',
              type: 'porosity',
              icon: { type: 'svg', value: svg.porosity },
              description: '',
              category: 'processOptimization',
              nodeType: 'Compute',
              inputType: [],
              outputType: [],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
        ],
      },
      {
        name: '数据组件',
        nodes: [
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '数值',
              type: 'numeric',
              icon: { type: 'svg', value: svg.numeric },
              description: '',
              category: 'processOptimization',
              nodeType: 'Data',
              inputType: [],
              outputType: [],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '3D结构',
              type: 'threeDStructure',
              icon: { type: 'svg', value: svg.threeDStructure },
              description: '',
              category: 'processOptimization',
              nodeType: 'Data',
              inputType: [],
              outputType: [],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '坐标数据',
              type: 'coordinateData',
              icon: { type: 'svg', value: svg.coordinateData },
              description: '',
              category: 'processOptimization',
              nodeType: 'Data',
              inputType: [],
              outputType: [],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
          {
            id: nanoid(),
            type: 'custom',
            data: {
              label: '线图',
              type: 'lineChart',
              icon: { type: 'svg', value: svg.lineChart },
              description: '',
              category: 'processOptimization',
              nodeType: 'Data',
              inputType: [],
              outputType: [],
              params: {
                concentration: 0,
                volume: 0,
              },
            },
          },
        ],
      },
    ],
  },
  电池管理: {
    icon: { type: 'icon', value: 'fluent-emoji-high-contrast:battery' },
    // icon: 'material-symbols:battery-charging-full-sharp',
    enabled: false,
    isBuiltin: true,
    name: '电池管理',
    type: 'batteryManagement',
    description: '电池管理模块',
    categories: [],
  },
  电池数据管理: {
    icon: { type: 'icon', value: 'fluent-emoji-high-contrast:battery' },
    // icon: 'material-symbols:battery-charging-full-sharp',
    enabled: true,
    isBuiltin: true,
    name: '电池数据管理',
    type: 'batteryDataManagement',
    description: '电池数据模块',
    categories: [
      {
        name: '基础组件',
        nodes: [
          {
            data: {
              category: 'materialDesign',
              description: '',
              icon: {
                type: 'svg',
                value: svg.batteryDataInput,
              },
              inputType: [],
              label: '电池数据库',
              nodeType: 'Basic',
              outputType: ['BatteryDataChart'],
              params: {
                BatteryId: '1',
                cycle: '100',
              },
              type: 'BatteryDatabase',
            },
            id: '1',
            type: 'BatteryDatabase',
          },
        ],
      },
      {
        name: '计算组件',
        nodes: [
          {
            data: {
              category: 'materialDesign',
              description: '',
              icon: {
                type: 'svg',
                value: svg.batteryCalibration,
              },
              label: '电池数据图表',
              nodeType: 'Compute',
              inputType: ['BatteryDatabase'],
              outputType: [''],
              params: {},
              type: 'BatteryDataChart',
            },
            id: '3',
            type: 'BatteryDataChart',
          },
        ],
      },
    ],
  },
  材料数据管理: {
    icon: { type: 'svg', value: svg.materialCalculation },
    // icon: 'material-symbols:battery-charging-full-sharp',
    enabled: true,
    isBuiltin: true,
    name: '材料数据管理',
    type: 'materialDataManagement',
    description: '材料数据管理模块',
    categories: [
      {
        name: '基础组件',
        nodes: [
          {
            data: {
              category: 'materialDataManagement',
              description: '',
              icon: {
                type: 'svg',
                value: svg.batteryDataInput,
              },
              inputType: [],
              label: '材料数据库',
              nodeType: 'Basic',
              outputType: ['energyCalculation', 'stablePhaseCalc'],
              params: {
                BatteryId: '1',
                cycle: '100',
              },
              type: 'materialDatabase',
            },
            id: '4',
            type: 'custom',
          },
        ],
      },
      {
        name: '计算组件',
        nodes: [
          {
            data: {
              category: 'materialDataManagement',
              description: '',
              icon: {
                type: 'svg',
                value: svg.batteryCalibration,
              },
              label: '能量计算',
              nodeType: 'Compute',
              inputType: ['materialDatabase'],
              outputType: [],
              params: {},
              type: 'energyCalculation',
            },
            id: '3',
            type: 'custom',
          },
          {
            data: {
              category: 'materialDataManagement',
              description: '',
              icon: {
                type: 'svg',
                value: svg.batteryCalibration,
              },
              label: '稳定相计算',
              nodeType: 'Compute',
              inputType: ['materialDatabase'],
              outputType: [],
              params: {},
              type: 'stablePhaseCalc',
            },
            id: '3',
            type: 'custom',
          },
        ],
      },
    ],
  },
}
export default nodeDetail
