/**
 * 将字符串从 snake_case 转换为 camelCase
 * @param str snake_case 格式的字符串
 * @returns camelCase 格式的字符串
 */
export function toCamelCase(str: string): string {
  return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase())
}

/**
 * 递归地将对象的所有属性从 snake_case 转换为 camelCase
 * @param obj 要转换的对象
 * @returns 转换后的对象
 */
export function convertToCamelCase(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map((item) => convertToCamelCase(item))
  }
  if (obj !== null && typeof obj === 'object') {
    return Object.keys(obj).reduce((result, key) => {
      const camelKey = toCamelCase(key)
      result[camelKey] = convertToCamelCase(obj[key])
      return result
    }, {} as any)
  }
  return obj
}


