/**
 * 渲染进程日志工具
 * 通过 IPC 将日志发送到主进程
 */

interface LogMeta {
  [key: string]: any
}

class RendererLogger {
  private isElectronAvailable(): boolean {
    return typeof window !== 'undefined' &&
           (window as any).electronAPI &&
           typeof ((window as any).electronAPI as any)['logger:info'] === 'function'
  }

  private async sendLog(level: string, message: string, meta?: any, error?: any): Promise<void> {
    if (this.isElectronAvailable()) {
      try {
        const electronAPI = (window as any).electronAPI
        const channelName = `logger:${level}`
        
        if (level === 'error' && error) {
          await electronAPI[channelName](message, error, meta)
        } else {
          await electronAPI[channelName](message, meta)
        }
      } catch (err) {
        console.error('Failed to send log to main process:', err)
        this.fallbackToConsole(level, message, meta || error)
      }
    } else {
      this.fallbackToConsole(level, message, meta || error)
    }
  }

  private fallbackToConsole(level: string, message: string, data?: any): void {
    const timestamp = new Date().toLocaleTimeString()
    const logMessage = `[${timestamp}] [${level.toUpperCase()}] ${message}`
    
    switch (level) {
      case 'error':
        console.error(logMessage, data)
        break
      case 'warn':
        console.warn(logMessage, data)
        break
      case 'info':
        console.info(logMessage, data)
        break
      case 'debug':
        console.debug(logMessage, data)
        break
      default:
        console.log(logMessage, data)
    }
  }

  /**
   * 记录信息日志
   */
  async info(message: string, meta?: LogMeta): Promise<void> {
    return this.sendLog('info', message, meta)
  }

  /**
   * 记录警告日志
   */
  async warn(message: string, meta?: LogMeta): Promise<void> {
    return this.sendLog('warn', message, meta)
  }

  /**
   * 记录错误日志
   */
  async error(message: string, error?: Error | any, meta?: LogMeta): Promise<void> {
    return this.sendLog('error', message, meta, error)
  }

  /**
   * 记录调试日志
   */
  async debug(message: string, meta?: LogMeta): Promise<void> {
    return this.sendLog('debug', message, meta)
  }

  /**
   * 记录用户操作日志
   */
  async userAction(action: string, meta?: LogMeta): Promise<void> {
    return this.info(`用户操作: ${action}`, {
      action,
      timestamp: new Date().toISOString(),
      ...meta
    })
  }

  /**
   * 记录页面访问日志
   */
  async pageView(page: string, meta?: LogMeta): Promise<void> {
    return this.info(`页面访问: ${page}`, {
      page,
      timestamp: new Date().toISOString(),
      ...meta
    })
  }

  /**
   * 记录 API 调用日志
   */
  async apiCall(api: string, duration?: number, meta?: LogMeta): Promise<void> {
    return this.info(`API 调用: ${api}`, {
      api,
      duration,
      timestamp: new Date().toISOString(),
      ...meta
    })
  }

  /**
   * 记录性能日志
   */
  async performance(operation: string, duration: number, meta?: LogMeta): Promise<void> {
    return this.info(`性能: ${operation} 耗时 ${duration}ms`, {
      operation,
      duration,
      performance: true,
      timestamp: new Date().toISOString(),
      ...meta
    })
  }
}

// 导出单例实例
export const logger = new RendererLogger()

// 默认导出
export default logger

// 类型定义
export type { LogMeta }
