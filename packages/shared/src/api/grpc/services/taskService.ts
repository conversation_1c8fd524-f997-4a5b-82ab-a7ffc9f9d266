/**
 * 任务相关的 gRPC 服务
 */
import { BaseService,BaseResponse } from './baseService'
import { Task } from '../types/task'
// 任务列表结果
export interface TaskResult {
  taskId: string
  taskList: Task[]
}

// 任务列表响应
export interface TaskResponse extends BaseResponse {
  taskResult: TaskResult
}

// 任务结果响应
export interface TaskResultResponse extends BaseResponse {
  result: string
}

// 同步服务请求响应
export interface DefaultServiceResponse {
  status: string
  message: string
  statusCode: number
  keyTypePairs: Record<string, string>
  keyValuePairs: Record<string, string>
}

export interface CallTaskResponse {
  status: string
  message: string
  result: string
  statusCode: number
}

// 异步服务请求响应
export interface SubmitTaskResponse {
  status: string
  message: string
  statusCode: number
  taskId: string
}

// 服务请求参数
export interface ServiceParams {
  serverId?: string
  serviceName?: string
  isSave?: boolean
  keyValuePairs?: Record<string, any>
  keyTypePairs?: Record<string, string>
}

export class TaskService extends BaseService {
/**
   * 获取任务列表
   * @param taskId 可选的特定任务ID
   */
  async getTaskList(taskId?: string): Promise<TaskResponse> {
    return this.call<TaskResponse>('getTaskList', {
      id: taskId,
    })
  }

  /**
   * 获取任务结果
   * @param taskId 任务ID
   */
  async getTaskResult(taskId: string): Promise<TaskResultResponse> {
    return this.call<TaskResultResponse>('getTaskResult', {
      id: taskId,
    })
  }

  /**
   * 删除任务
   * @param taskId 任务ID
   */
  async deleteTask(taskId: string): Promise<BaseResponse> {
    return this.call<BaseResponse>('deleteTask', {
      id: taskId,
    })
  }

  /**
   * 停止任务
   * @param taskId 任务ID
   */
  async stopTask(taskId: string): Promise<BaseResponse> {
    return this.call<BaseResponse>('stopTask', {
      id: taskId,
    })
  }

  /**
   * 暂停任务
   * @param taskId 任务ID
   */
  async pauseTask(taskId: string): Promise<BaseResponse> {
    return this.call<BaseResponse>('pauseTask', {
      id: taskId,
    })
  }

  /**
   * 恢复任务
   * @param taskId 任务ID
   */
  async resumeTask(taskId: string): Promise<BaseResponse> {
    return this.call<BaseResponse>('resumeTask', {
      id: taskId,
    })
  }

  /**
   * 提交同步任务请求
   */
  async callTask(
    serviceName: string,
    serverId: string,
    params: Record<string, any>,
  ): Promise<CallTaskResponse> {
    const response = await this.default<DefaultServiceResponse>(
      serviceName,
      serverId,
      false,
      params,
    )
    return {
      status: response.statusCode === 200 ? 'Success' : 'Failed',
      statusCode: response.statusCode,
      message: response.message,
      result: response.keyValuePairs.result,
    }
  }

  /**
   * 提交异步任务请求
   */
  async submitTask(
    serviceName: string,
    serverId: string,
    params: Record<string, any>,
  ): Promise<SubmitTaskResponse> {
    const response = await this.submit<SubmitTaskResponse>(serviceName, serverId, true, params)
    return {
      status: response.statusCode === 200 ? 'Success' : 'Failed',
      message: response.message,
      taskId: response.taskId,
      statusCode: response.statusCode,
    }
  }
}

// 导出单例实例
export const taskService = new TaskService()
