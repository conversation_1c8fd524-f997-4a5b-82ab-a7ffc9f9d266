/**
 * 服务器相关的 gRPC 服务
 */
import { BaseService , BaseResponse} from './baseService'
import { Server, ServerUsage } from '../types/server'

export interface ServerResult {
  serverId: string
  serverList: Server[]
}

export interface ServerResponse extends BaseResponse {
  serverResult: ServerResult
}

export interface ServerUsageResponse extends BaseResponse {
  serverUsage: ServerUsage
}

export interface GetVersionResponse extends BaseResponse {
  result: string
  result_type:string
}

export class ServerService extends BaseService {
  /**
   * 获取服务器列表
   * @param params 请求参数
   * @returns Promise<ServerResponse>
   */
  async getServerList(serverId: string): Promise<ServerResponse> {
    return this.call<ServerResponse>('getServerList', {
      id: serverId,
    })
  }

    /**
   * 删除服务器信息
   * @param serverId 服务器ID
   */
  async deleteServerInfo(serverId: string): Promise<BaseResponse> {
    return this.call<BaseResponse>('deleteServerInfo', {
      id: serverId,
    })
  }

  /**
   * 获取服务器资源使用情况
   * @param serverId 服务器ID
   */
  async getServerUsage(serverId: string): Promise<ServerUsageResponse> {
    return this.call<ServerUsageResponse>('getServerUsage', {
      id: serverId,
    })
  }

  /**
   * 获取中台版本信息
   * @param params 请求参数
   * @returns Promise<GetVersionResponse>
   */
  async getVersion(): Promise<GetVersionResponse> {
    return this.call<GetVersionResponse>('getVersion', {})
  }
}

// 导出单例实例
export const serverService = new ServerService()
