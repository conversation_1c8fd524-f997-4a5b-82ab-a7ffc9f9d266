/**
 * 主题配置 - 统一使用 Tailwind v4 主题系统
 * 主题定义已迁移到 packages/configs/src/styles/theme.css
 */

// 主题名称常量
export const THEME_NAMES = {
  LIGHT: 'light',
  DARK: 'dark',
  BLUE: 'blue',
  GREEN: 'green',
  PURPLE: 'purple',
  VIOLET: 'violet',
  ROSE: 'rose',
  ZINC: 'zinc',
  SLATE: 'slate',
}

// 主题切换工具函数
export function setTheme(themeName) {
  if (typeof document !== 'undefined') {
    document.documentElement.setAttribute('data-theme', themeName)
  }
}

// 获取当前主题
export function getCurrentTheme() {
  if (typeof document !== 'undefined') {
    return document.documentElement.getAttribute('data-theme') || THEME_NAMES.LIGHT
  }
  return THEME_NAMES.LIGHT
}

// 切换暗色模式
export function toggleDarkMode() {
  if (typeof document !== 'undefined') {
    const isDark = document.documentElement.classList.contains('dark')
    if (isDark) {
      document.documentElement.classList.remove('dark')
      setTheme(THEME_NAMES.LIGHT)
    } else {
      document.documentElement.classList.add('dark')
      setTheme(THEME_NAMES.DARK)
    }
  }
}

// 检查是否为暗色模式
export function isDarkMode() {
  if (typeof document !== 'undefined') {
    return document.documentElement.classList.contains('dark')
  }
  return false
}
