/**
 * Electron-Vite 基础配置
 */
import { defineConfig } from 'electron-vite'
import { resolve } from 'path'
import { fileURLToPath } from 'url'
import { dirname } from 'path'
import vue from '@vitejs/plugin-vue'
import { createAutoImportPlugins } from './plugins'

// ES 模块中获取 __dirname
const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

export function createElectronConfig(options: {
  appName: string
  mainEntry?: string
  preloadEntry?: string
  rendererEntry?: string
  rendererTemplate?: string
  [key: string]: any
}) {
  const { appName, ...otherOptions } = options

  return defineConfig({
    main: {
      build: {
        rollupOptions: {
          external: ['@mattverse/electron-core'],
        },
      },
      resolve: {
        alias: {
          '@': resolve(__dirname, `../../../apps/${appName}/src/main`),
          '@core': resolve(__dirname, '../../electron-core/src'),
        },
      },
    },
    preload: {
      build: {
        rollupOptions: {
          external: ['@mattverse/electron-core'],
        },
      },
      resolve: {
        alias: {
          '@': resolve(__dirname, `../../../apps/${appName}/src/preload`),
          '@core': resolve(__dirname, '../../electron-core/src'),
        },
      },
    },
    renderer: {
      plugins: [vue(), ...createAutoImportPlugins({ appName })],
      resolve: {
        alias: {
          '@': resolve(__dirname, `../../../apps/${appName}/src/renderer/src`),
          '@ui': resolve(__dirname, '../../mattverse-ui/src'),
          '@flow': resolve(__dirname, '../../mattverse-flow/src'),
        },
      },
    },
    ...otherOptions,
  })
}

/**
 * 预设配置工厂函数
 * 应用可以使用这些工厂函数创建自己的配置
 */
export const electronPresets = {
  /** 默认配置 */
  default: (appName: string, options: Omit<Parameters<typeof createElectronConfig>[0], 'appName'> = {}) =>
    createElectronConfig({ appName, ...options }),

  /** 自定义配置 */
  custom: (options: Parameters<typeof createElectronConfig>[0]) =>
    createElectronConfig(options),
}
