/**
 * Electron 缓存管理处理器
 */
import { session, app } from 'electron'
import { promises as fs } from 'fs'
import { join } from 'path'
import { logger } from '../utils/logger'

/**
 * 缓存类型枚举
 */
export type CacheType =
  | 'http'
  | 'indexeddb'
  | 'localstorage'
  | 'sessionstorage'
  | 'cookies'
  | 'appcache'
  | 'serviceworkers'
  | 'pinia'
  | 'app-settings'

/**
 * 缓存信息接口
 */
export interface CacheInfo {
  size: number // MB
  entries: number
  lastModified?: string
}

/**
 * 获取目录大小（递归）
 */
async function getDirectorySize(dirPath: string): Promise<number> {
  try {
    const stats = await fs.stat(dirPath)
    if (!stats.isDirectory()) {
      return stats.size
    }

    const files = await fs.readdir(dirPath)
    let totalSize = 0

    for (const file of files) {
      const filePath = join(dirPath, file)
      try {
        const fileStats = await fs.stat(filePath)
        if (fileStats.isDirectory()) {
          totalSize += await getDirectorySize(filePath)
        } else {
          totalSize += fileStats.size
        }
      } catch (error) {
        // 忽略无法访问的文件
        logger.warn(`无法访问文件: ${filePath}`, error)
      }
    }

    return totalSize
  } catch (error) {
    logger.warn(`无法访问目录: ${dirPath}`, error)
    return 0
  }
}

/**
 * 字节转换为 MB
 */
function bytesToMB(bytes: number): number {
  return Math.round((bytes / (1024 * 1024)) * 100) / 100
}

/**
 * 获取缓存目录路径
 */
function getCachePaths() {
  const userDataPath = app.getPath('userData')
  return {
    http: join(userDataPath, 'Cache'),
    gpu: join(userDataPath, 'GPUCache'),
    shader: join(userDataPath, 'ShaderCache'),
    indexeddb: join(userDataPath, 'IndexedDB'),
    localstorage: join(userDataPath, 'Local Storage'),
    sessionstorage: join(userDataPath, 'Session Storage'),
    cookies: join(userDataPath, 'Cookies'),
    logs: join(userDataPath, 'logs'),
    temp: join(userDataPath, 'temp'),
  }
}

/**
 * 缓存处理器
 */
export const cacheHandlers = {
  /**
   * 获取所有缓存信息
   */
  'cache:get-info': async (): Promise<Record<string, CacheInfo>> => {
    try {
      const cachePaths = getCachePaths()
      const cacheInfo: Record<string, CacheInfo> = {}

      // HTTP 缓存
      const httpSize = await getDirectorySize(cachePaths.http)
      const gpuSize = await getDirectorySize(cachePaths.gpu)
      const shaderSize = await getDirectorySize(cachePaths.shader)
      cacheInfo.http = {
        size: bytesToMB(httpSize + gpuSize + shaderSize),
        entries: 0, // HTTP 缓存条目数难以准确计算
      }

      // IndexedDB
      const indexeddbSize = await getDirectorySize(cachePaths.indexeddb)
      cacheInfo.indexeddb = {
        size: bytesToMB(indexeddbSize),
        entries: 0,
      }

      // LocalStorage
      const localstorageSize = await getDirectorySize(cachePaths.localstorage)
      cacheInfo.localstorage = {
        size: bytesToMB(localstorageSize),
        entries: 0,
      }

      // SessionStorage (通常在内存中，大小为 0)
      cacheInfo.sessionstorage = {
        size: 0,
        entries: 0,
      }

      // Cookies
      const cookiesSize = await getDirectorySize(cachePaths.cookies)
      cacheInfo.cookies = {
        size: bytesToMB(cookiesSize),
        entries: 0,
      }

      // 其他缓存类型（模拟数据）
      cacheInfo.appcache = { size: 0, entries: 0 }
      cacheInfo.serviceworkers = { size: 0, entries: 0 }
      cacheInfo.pinia = { size: 0.1, entries: 1 }
      cacheInfo['app-settings'] = { size: 0.05, entries: 1 }

      logger.info('缓存信息获取成功', cacheInfo)
      return cacheInfo
    } catch (error) {
      logger.error('获取缓存信息失败:', error)
      throw error
    }
  },

  /**
   * 清理指定类型的缓存
   */
  'cache:clear-types': async (
    types: CacheType[]
  ): Promise<{ success: boolean; cleared: CacheType[] }> => {
    try {
      const cleared: CacheType[] = []
      const defaultSession = session.defaultSession

      for (const type of types) {
        try {
          switch (type) {
            case 'http':
              await defaultSession.clearCache()
              cleared.push(type)
              break

            case 'cookies':
              await defaultSession.clearStorageData({ storages: ['cookies'] })
              cleared.push(type)
              break

            case 'localstorage':
              await defaultSession.clearStorageData({ storages: ['localstorage'] })
              cleared.push(type)
              break

            case 'sessionstorage':
              // SessionStorage 通常在内存中，重新加载页面即可清理
              cleared.push(type)
              break

            case 'indexeddb':
              await defaultSession.clearStorageData({ storages: ['indexdb'] })
              cleared.push(type)
              break

            case 'appcache':
              // AppCache 已废弃，但仍可通过 filesystem 清理
              await defaultSession.clearStorageData({ storages: ['filesystem'] })
              cleared.push(type)
              break

            case 'serviceworkers':
              await defaultSession.clearStorageData({ storages: ['serviceworkers'] })
              cleared.push(type)
              break

            case 'pinia':
            case 'app-settings':
              // 这些是应用级缓存，需要特殊处理
              logger.info(`清理应用缓存: ${type}`)
              cleared.push(type)
              break

            default:
              logger.warn(`未知的缓存类型: ${type}`)
          }
        } catch (error) {
          logger.error(`清理缓存失败 ${type}:`, error)
        }
      }

      logger.info('缓存清理完成', { requested: types, cleared })
      return { success: true, cleared }
    } catch (error) {
      logger.error('缓存清理失败:', error)
      return { success: false, cleared: [] }
    }
  },

  /**
   * 清理所有缓存
   */
  'cache:clear-all': async (): Promise<{ success: boolean }> => {
    try {
      const defaultSession = session.defaultSession

      // 清理所有存储数据
      await defaultSession.clearStorageData({
        storages: [
          'cookies',
          'filesystem',
          'indexdb',
          'localstorage',
          'shadercache',
          'websql',
          'serviceworkers',
          'cachestorage',
        ],
      })

      // 清理 HTTP 缓存
      await defaultSession.clearCache()

      logger.info('所有缓存清理完成')
      return { success: true }
    } catch (error) {
      logger.error('清理所有缓存失败:', error)
      return { success: false }
    }
  },

  /**
   * 设置缓存大小限制
   */
  'cache:set-size-limit': async (
    type: CacheType,
    sizeMB: number
  ): Promise<{ success: boolean }> => {
    try {
      // Electron 的缓存大小限制主要通过 session 配置
      // 这里主要是记录设置，实际限制需要在应用启动时配置
      logger.info(`设置缓存大小限制: ${type} = ${sizeMB}MB`)

      // TODO: 实现具体的缓存大小限制逻辑
      // 可以通过修改 session 配置或使用配额管理 API

      return { success: true }
    } catch (error) {
      logger.error('设置缓存大小限制失败:', error)
      return { success: false }
    }
  },

  /**
   * 根据设置执行自动清理
   */
  'cache:auto-clean': async (settings: {
    enabled: boolean
    interval: 'daily' | 'weekly' | 'monthly' | 'never'
    maxAge: number
    enabledTypes: CacheType[]
  }): Promise<{ success: boolean; cleaned: CacheType[] }> => {
    try {
      if (!settings.enabled || settings.interval === 'never') {
        return { success: true, cleaned: [] }
      }

      logger.info('执行自动缓存清理', settings)

      // 清理启用的缓存类型
      const result = await cacheHandlers['cache:clear-types'](settings.enabledTypes)

      return {
        success: result.success,
        cleaned: result.cleared || [],
      }
    } catch (error) {
      logger.error('自动缓存清理失败:', error)
      return { success: false, cleaned: [] }
    }
  },

  /**
   * 应用退出时清理缓存
   */
  'cache:clear-on-exit': async (enabledTypes: CacheType[]): Promise<{ success: boolean }> => {
    try {
      logger.info('应用退出时清理缓存', enabledTypes)

      if (enabledTypes.length === 0) {
        return { success: true }
      }

      const result = await cacheHandlers['cache:clear-types'](enabledTypes)
      return { success: result.success }
    } catch (error) {
      logger.error('退出时缓存清理失败:', error)
      return { success: false }
    }
  },

  /**
   * 更新缓存管理器设置
   */
  'cache:update-manager-settings': async (settings: {
    enableCache: boolean
    clearOnExit: boolean
    autoClean: {
      enabled: boolean
      interval: 'daily' | 'weekly' | 'monthly' | 'never'
      maxAge: number
    }
    types: Record<CacheType, { enabled: boolean; maxSize: number; autoClean: boolean }>
  }): Promise<{ success: boolean }> => {
    try {
      logger.info('更新缓存管理器设置', settings)

      // 这里只是记录设置更新，实际的缓存管理器更新会在渲染进程中处理
      // 或者通过其他方式同步设置

      return { success: true }
    } catch (error) {
      logger.error('更新缓存管理器设置失败:', error)
      return { success: false }
    }
  },
}
