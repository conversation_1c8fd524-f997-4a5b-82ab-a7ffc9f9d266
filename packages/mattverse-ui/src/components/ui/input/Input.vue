<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { useVModel } from '@vueuse/core'
import { computed, useAttrs } from 'vue'
import { cn } from '@/lib/utils'

const props = defineProps<{
  defaultValue?: string | number
  modelValue?: string | number
  class?: HTMLAttributes['class']
  size?: 'sm' | 'md' | 'lg' | number
}>()

const emits = defineEmits<{
  'update:modelValue': [payload: string | number]
}>()

const attrs = useAttrs()

const modelValue = useVModel(props, 'modelValue', emits, {
  passive: true,
  defaultValue: props.defaultValue,
})

// 处理size属性，将字符串size转换为CSS类，数字size保留原样
const sizeClass = computed(() => {
  if (typeof props.size === 'string') {
    const sizeMap = {
      sm: 'h-8 px-2 py-1 text-sm',
      md: 'h-9 px-3 py-1',
      lg: 'h-10 px-4 py-2'
    }
    return sizeMap[props.size] || sizeMap.md
  }
  return 'h-9 px-3 py-1' // 默认大小
})

// 过滤掉size属性，避免传递给原生input
const filteredAttrs = computed(() => {
  const result = { ...attrs }
  delete result.size
  return result
})

// 只有当size是数字时才传递给原生input
const nativeSize = computed(() => {
  return typeof props.size === 'number' ? props.size : undefined
})
</script>

<template>
  <input
    v-model="modelValue"
    v-bind="filteredAttrs"
    :size="nativeSize"
    data-slot="input"
    :class="cn(
      'file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex w-full min-w-0 rounded-md border bg-transparent text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm',
      'focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]',
      'aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive',
      sizeClass,
      props.class,
    )"
  >
</template>
