<template>
  <div class="json-data-renderer">
    <!-- 对象类型数据 -->
    <Card v-if="isObject && expanded" class="mb-2">
      <CardHeader class="pb-2">
        <div class="flex items-center justify-between">
          <CardTitle class="text-sm">{{ propertyName || '对象' }}</CardTitle>
          <Badge variant="secondary" class="text-xs">
            {{ Object.keys(data).length }} 属性
          </Badge>
        </div>
      </CardHeader>
      <CardContent class="pt-0">
        <div v-if="Object.keys(data).length > 0" class="space-y-2">
          <div v-for="(value, key) in data" :key="key" class="p-2 border border-border/50 rounded-md">
            <div class="flex justify-between items-start mb-1">
              <span class="font-medium text-sm">{{ key }}</span>
              <Badge
                v-if="typeof value === 'object' && value !== null"
                variant="outline"
                class="text-xs"
              >
                {{
                  Array.isArray(value)
                    ? `数组 (${value.length})`
                    : `对象 (${Object.keys(value).length})`
                }}
              </Badge>
              <Badge
                v-else-if="
                  typeof value === 'string' ||
                  typeof value === 'number' ||
                  typeof value === 'boolean' ||
                  value === null ||
                  typeof value === 'undefined'
                "
                variant="outline"
                class="text-xs"
              >
                {{ value === null ? '空值' : typeof value === 'undefined' ? '未定义' : typeof value }}
              </Badge>
            </div>
            <div class="whitespace-pre-wrap break-words text-sm">
              <template v-if="typeof value === 'object' && value !== null">
                <MattJsonDataRenderer
                  :data="value"
                  :property-name="key"
                  :depth="depth + 1"
                  :expanded="false"
                />
              </template>
              <template v-else>
                {{ formatValue(value) }}
              </template>
            </div>
          </div>
        </div>
        <div v-else class="text-xs text-muted-foreground text-center py-2">空对象</div>
      </CardContent>
    </Card>

    <!-- 数组类型数据 -->
    <Card v-else-if="isArray && expanded" class="mb-2">
      <CardHeader class="pb-2">
        <div class="flex items-center justify-between">
          <CardTitle class="text-sm">{{ propertyName || '数组' }}</CardTitle>
          <Badge variant="secondary" class="text-xs">{{ data.length }} 项</Badge>
        </div>
      </CardHeader>
      <CardContent class="pt-0">
        <div v-if="data.length > 0" class="space-y-2">
          <div v-for="(item, index) in data" :key="index">
            <!-- 数组元素是对象或数组 -->
            <div v-if="typeof item === 'object' && item !== null" class="border border-border/50 rounded-md">
              <div class="flex justify-between items-center p-2 bg-muted/30">
                <span class="font-medium text-sm">对象 {{ index + 1 }}</span>
                <Badge variant="outline" class="text-xs">
                  {{
                    isArray ? Object.keys(item).length + ' 项' : Object.keys(item).length + ' 属性'
                  }}
                </Badge>
              </div>
              <div class="p-2">
                <MattJsonDataRenderer
                  :data="item"
                  :property-name="'对象 ' + (index + 1)"
                  :depth="depth + 1"
                  :expanded="false"
                />
              </div>
            </div>
            <!-- 数组元素是基本类型 -->
            <div v-else class="p-2 border border-border/50 rounded-md mb-2">
              <div class="flex justify-between">
                <span class="font-medium">对象 {{ index + 1 }}</span>
                <Badge
                  v-if="
                    typeof item === 'string' ||
                    typeof item === 'number' ||
                    typeof item === 'undefined'
                  "
                  variant="outline"
                  class="text-xs"
                >
                  {{ typeof item === 'undefined' ? '未定义' : typeof item }}
                </Badge>
              </div>
              <div class="mt-1 whitespace-pre-wrap break-words">{{ formatValue(item) }}</div>
            </div>
          </div>
        </div>
        <div v-else class="text-xs text-muted-foreground text-center py-2">空数组</div>
      </CardContent>
    </Card>

    <!-- 基本类型数据 -->
    <div v-else class="p-3 border border-border rounded-md bg-card/30">
      <div class="text-sm">{{ formatValue(data) }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card'
import { Badge } from '../../ui/badge'

interface Props {
  data: any
  propertyName: string
  depth?: number
  expanded?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  depth: 0,
  expanded: true,
})

// 计算属性
const isObject = computed(
  () => props.data !== null && typeof props.data === 'object' && !Array.isArray(props.data)
)
const isArray = computed(() => Array.isArray(props.data))

// 格式化值显示
const formatValue = (value: any): string => {
  if (value === null) return 'null'
  if (value === undefined) return 'undefined'
  if (typeof value === 'string') return `"${value}"`
  if (typeof value === 'boolean') return value ? 'true' : 'false'
  if (typeof value === 'number') return value.toString()
  if (typeof value === 'object') {
    if (Array.isArray(value)) {
      return `[${value.length} 项]`
    }
    return `{${Object.keys(value).length} 属性}`
  }
  return String(value)
}
</script>

<style scoped>
.json-data-renderer {
  width: 100%;
}
</style>
