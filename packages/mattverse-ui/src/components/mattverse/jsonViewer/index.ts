/**
 * JSON 查看器组件
 * 提供 JSON 数据的可视化查看、编辑和操作功能
 */

export { default as MattJsonViewer } from './MattJsonViewer.vue'
export { default as MattJsonTreeNode } from './MattJsonTreeNode.vue'
export { default as MattJsonDataRenderer } from './MattJsonDataRenderer.vue'

// 导出类型定义
export interface JsonViewerProps {
  initialData?: any
  isOpen?: boolean
  onSave?: (data: any) => void
}

export interface JsonTreeNodeProps {
  data: any
  level: number
  path: string
  keyName?: string
  expandedPaths: Set<string>
  searchText: string
  selectedPath: string
}

export interface JsonDataRendererProps {
  data: any
  propertyName: string
  depth?: number
  expanded?: boolean
}
