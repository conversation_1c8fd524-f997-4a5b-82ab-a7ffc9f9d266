<template>
  <Dialog :open="isOpen" @update:open="updateOpen">
    <DialogContent class="max-w-6xl h-[80vh] p-4">
      <div class="flex flex-col h-full overflow-auto">
        <!-- 标题栏 -->
        <div class="flex items-center justify-between mb-4 mr-6">
          <div class="flex items-center gap-2">
            <FileText class="h-5 w-5" />
            <h2 class="text-lg font-semibold">JSON 查看器</h2>
          </div>
          <div class="flex items-center gap-2">
            <Button variant="outline" size="sm" @click="formatJson" :disabled="!jsonInput.trim()">
              <FileCode class="h-4 w-4 mr-1" />
              格式化
            </Button>
            <Button variant="outline" size="sm" @click="compactJson" :disabled="!jsonInput.trim()">
              <FileMinus class="h-4 w-4 mr-1" />
              压缩
            </Button>
            <Button variant="outline" size="sm" @click="copyJson" :disabled="!jsonInput.trim()">
              <Copy class="h-4 w-4 mr-1" />
              复制
            </Button>
            <!-- <Button variant="ghost" size="sm" @click="updateOpen(false)">
              <X class="h-4 w-4" />
            </Button> -->
          </div>
        </div>

        <!-- 错误提示 -->
        <div
          v-if="error"
          class="mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-lg"
        >
          <div class="text-destructive text-sm">{{ error }}</div>
        </div>

        <!-- 主要内容区域 - 使用 ResizablePanel -->
        <ResizablePanelGroup direction="horizontal" class="flex-1 min-h-0">
          <!-- 左侧：JSON 编辑器 -->
          <ResizablePanel :default-size="33" :min-size="25">
            <div class="h-full flex flex-col border rounded-lg mr-2">
              <div class="flex items-center justify-between p-3 border-b bg-muted/30">
                <h3 class="font-medium text-sm">JSON 数据</h3>
                <div class="flex gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    @click="compactAndEscapeJson"
                    :disabled="!jsonInput.trim()"
                  >
                    转义
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    @click="unescapeJson"
                    :disabled="!jsonInput.trim()"
                  >
                    去转义
                  </Button>
                </div>
              </div>
              <div class="flex-1 overflow-auto">
                <ScrollArea class="h-full">
                  <div
                    ref="codeEditorRef"
                    class="font-mono p-2 outline-none whitespace-pre-wrap min-w-full break-all rounded-md cursor-pointer bg-background text-foreground"
                    contenteditable="true"
                    @input="handleEditorInput"
                    @blur="formatOnBlur"
                    v-html="highlightedJson"
                  ></div>
                </ScrollArea>
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle />

          <!-- 中间：JSON 树形视图 -->
          <ResizablePanel :default-size="34" :min-size="25">
            <div class="h-full flex flex-col border rounded-lg mx-1">
              <div class="p-3 border-b bg-muted/30">
                <div class="flex items-center justify-between mb-2">
                  <h3 class="font-medium text-sm">树形视图</h3>
                  <div class="flex gap-1">
                    <Button variant="ghost" size="sm" @click="expandAll" :disabled="!parsedJson">
                      展开全部
                    </Button>
                    <Button variant="ghost" size="sm" @click="collapseAll" :disabled="!parsedJson">
                      折叠全部
                    </Button>
                  </div>
                </div>
                <Input
                  v-model="searchText"
                  placeholder="搜索..."
                  class="h-8 text-xs"
                  :disabled="!parsedJson"
                />
              </div>
              <div class="flex-1 overflow-auto">
                <ScrollArea class="h-full">
                  <div class="p-3">
                    <div v-if="parsedJson">
                      <MattJsonTreeNode
                        :data="parsedJson"
                        :level="0"
                        path="root"
                        :expanded-paths="expandedPaths"
                        :search-text="searchText"
                        :selected-path="selectedPath"
                        @toggle="togglePath"
                        @select="selectNode"
                      />
                    </div>
                    <div v-else class="text-muted-foreground text-sm text-center py-8">
                      请输入有效的 JSON 数据
                    </div>
                  </div>
                </ScrollArea>
              </div>
            </div>
          </ResizablePanel>

          <ResizableHandle />

          <!-- 右侧：属性详情 -->
          <ResizablePanel :default-size="33" :min-size="25">
            <div class="h-full flex flex-col border rounded-lg ml-2">
              <div class="p-3 border-b bg-muted/30">
                <h3 class="font-medium text-sm">属性详情</h3>
              </div>
              <div class="flex-1 overflow-auto">
                <ScrollArea class="h-full">
                  <div class="p-3">
                    <div v-if="selectedNodeData">
                      <div class="space-y-3">
                        <div class="text-sm font-medium">路径: {{ selectedPath }}</div>
                        <div class="space-y-1">
                          <div
                            v-for="(value, key) in selectedNodeProperties"
                            :key="key"
                            class="p-2 border rounded hover:bg-muted/50"
                          >
                            <div class="text-xs text-muted-foreground">{{ key }}</div>
                            <div class="text-sm font-mono">{{ value }}</div>
                          </div>
                        </div>
                        <div class="mt-4">
                          <MattJsonDataRenderer
                            :data="selectedNodeData"
                            :property-name="selectedPath"
                          />
                        </div>
                      </div>
                    </div>
                    <div v-else class="text-muted-foreground text-sm text-center py-8">
                      点击左侧节点查看详情
                    </div>
                  </div>
                </ScrollArea>
              </div>
            </div>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import { toast } from 'vue-sonner'
import { Copy, X, FileText, FileMinus, FileCode } from 'lucide-vue-next'
import { Dialog, DialogContent } from '../../ui/dialog'
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from '../../ui/resizable'
import { ScrollArea } from '../../ui/scroll-area'
import { Button } from '../../ui/button'
import { Input } from '../../ui/input'
import MattJsonTreeNode from './MattJsonTreeNode.vue'
import MattJsonDataRenderer from './MattJsonDataRenderer.vue'
import Prism from 'prismjs'
// import 'prismjs/themes/prism-tomorrow.css'
import 'prismjs/components/prism-json'

interface Props {
  initialData?: any
  isOpen?: boolean
  onSave?: (data: any) => void
}

const props = withDefaults(defineProps<Props>(), {
  isOpen: false,
})

const emit = defineEmits<{
  'update:isOpen': [value: boolean]
  save: [data: any]
}>()

// 响应式数据
const codeEditorRef = ref<HTMLElement>()
const jsonInput = ref('')
const parsedJson = ref<any>(null)
const error = ref('')
const expandedPaths = ref(new Set<string>())
const searchText = ref('')
const selectedPath = ref('')
const selectedNodeData = ref<any>(null)
const preventParseUpdate = ref(false)

// 计算属性
const isOpen = computed(() => props.isOpen)

// 高亮显示的 JSON
const highlightedJson = computed(() => {
  if (!jsonInput.value.trim()) return ''

  try {
    // 尝试格式化 JSON 以便更好地显示
    const formatted = JSON.stringify(JSON.parse(jsonInput.value), null, 2)
    // 使用 Prism 高亮显示
    return Prism.highlight(formatted, Prism.languages.json, 'json')
  } catch (e) {
    // 如果解析失败，仍然尝试高亮显示原始文本
    return Prism.highlight(jsonInput.value, Prism.languages.json, 'json')
  }
})

// 处理编辑器输入
const handleEditorInput = (event: Event) => {
  const target = event.target as HTMLElement
  const content = target.innerText || ''

  // 更新 jsonInput，但不立即解析，避免在输入过程中出现错误提示
  jsonInput.value = content
}

// 失去焦点时格式化
const formatOnBlur = () => {
  if (!jsonInput.value.trim()) return

  try {
    // 设置标志位，防止在格式化过程中触发解析
    preventParseUpdate.value = true

    const obj = JSON.parse(jsonInput.value)
    jsonInput.value = JSON.stringify(obj, null, 2)

    // 解析 JSON
    parsedJson.value = obj
    error.value = ''

    // 更新编辑器内容，确保自动换行
    nextTick(() => {
      if (codeEditorRef.value) {
        const formattedJson = JSON.stringify(JSON.parse(jsonInput.value), null, 2)
        codeEditorRef.value.innerHTML = Prism.highlight(formattedJson, Prism.languages.json, 'json')
        // 强制换行
        codeEditorRef.value.style.whiteSpace = 'pre-wrap'

        // 恢复标志位
        preventParseUpdate.value = false
      }
    })
  } catch (e) {
    // 解析失败时不做处理，保留用户输入
  }
}

// 初始化数据
const initData = () => {
  if (props.initialData) {
    try {
      const formattedJson = JSON.stringify(props.initialData, null, 2)
      jsonInput.value = formattedJson
      parsedJson.value = props.initialData
      expandedPaths.value.add('root')

      // 更新编辑器内容
      nextTick(() => {
        if (codeEditorRef.value) {
          codeEditorRef.value.innerHTML = highlightedJson.value
        }
      })
    } catch (e) {
      error.value = `初始化数据失败: ${(e as Error).message}`
    }
  } else {
    // 清空数据时也要更新编辑器
    jsonInput.value = ''
    parsedJson.value = null
    error.value = ''
    nextTick(() => {
      if (codeEditorRef.value) {
        codeEditorRef.value.innerHTML =
          '<span class="text-muted-foreground">请输入 JSON 数据...</span>'
      }
    })
  }
}

// 解析 JSON
const parseJson = () => {
  if (!jsonInput.value.trim()) {
    parsedJson.value = null
    error.value = ''
    return
  }

  try {
    parsedJson.value = JSON.parse(jsonInput.value)
    error.value = ''
  } catch (e) {
    error.value = `JSON 格式错误: ${(e as Error).message}`
    parsedJson.value = null
  }
}

// 防抖解析
const parseJsonDebounced = useDebounceFn(parseJson, 500)

// 格式化 JSON
const formatJson = () => {
  try {
    if (!jsonInput.value.trim()) return
    const obj = JSON.parse(jsonInput.value)
    const formattedJson = JSON.stringify(obj, null, 2)
    jsonInput.value = formattedJson
    parseJson()

    // 更新编辑器内容
    nextTick(() => {
      if (codeEditorRef.value) {
        const highlighted = Prism.highlight(formattedJson, Prism.languages.json, 'json')
        codeEditorRef.value.innerHTML = highlighted
      }
    })

    // 显示成功消息
    toast.success('格式化成功')
  } catch (e) {
    error.value = `格式化失败: ${(e as Error).message}`
  }
}

// 压缩 JSON（删除空格）
const compactJson = () => {
  try {
    if (!jsonInput.value.trim()) return
    const obj = JSON.parse(jsonInput.value)
    const compactedJson = JSON.stringify(obj)
    jsonInput.value = compactedJson
    parseJson()

    // 更新编辑器内容
    nextTick(() => {
      if (codeEditorRef.value) {
        const highlighted = Prism.highlight(compactedJson, Prism.languages.json, 'json')
        codeEditorRef.value.innerHTML = highlighted
      }
    })
    toast.success('压缩成功')
  } catch (e) {
    error.value = `压缩失败: ${(e as Error).message}`
  }
}

// 压缩并转义 JSON
const compactAndEscapeJson = () => {
  try {
    if (!jsonInput.value.trim()) return

    // 设置标志位，防止在处理过程中触发解析
    preventParseUpdate.value = true

    const obj = JSON.parse(jsonInput.value)
    const compacted = JSON.stringify(obj)
    const escaped = JSON.stringify(compacted)
    jsonInput.value = escaped

    // 手动解析，因为现在是字符串格式
    parsedJson.value = null
    error.value = ''

    // 更新编辑器内容，确保自动换行
    nextTick(() => {
      if (codeEditorRef.value) {
        try {
          const formattedJson = JSON.stringify(JSON.parse(jsonInput.value), null, 2)
          codeEditorRef.value.innerHTML = Prism.highlight(
            formattedJson,
            Prism.languages.json,
            'json'
          )
          // 强制换行
          codeEditorRef.value.style.whiteSpace = 'pre-wrap'
        } catch (err) {
          // 如果解析失败，显示原始内容
          codeEditorRef.value.innerHTML = jsonInput.value
        }

        // 恢复标志位
        preventParseUpdate.value = false
      }
    })

    toast.success('压缩并转义成功')
  } catch (e) {
    error.value = `压缩转义失败: ${(e as Error).message}`
    // 恢复标志位
    preventParseUpdate.value = false
  }
}

// 去除转义
const unescapeJson = () => {
  try {
    if (!jsonInput.value.trim()) return
    // 检查是否是转义的 JSON 字符串
    if (jsonInput.value.startsWith('"') && jsonInput.value.endsWith('"')) {
      const unescaped = JSON.parse(jsonInput.value)
      jsonInput.value = unescaped
      parseJson()

      // 更新编辑器内容
      nextTick(() => {
        if (codeEditorRef.value) {
          const highlighted = Prism.highlight(unescaped, Prism.languages.json, 'json')
          codeEditorRef.value.innerHTML = highlighted
        }
      })

      toast.success('去除转义成功')
    } else {
      toast.error('当前 JSON 不是转义格式')
    }
  } catch (e) {
    error.value = `去除转义失败: ${(e as Error).message}`
  }
}

// 复制 JSON 数据
const copyJson = async () => {
  try {
    await navigator.clipboard.writeText(jsonInput.value)
    toast.success('复制成功')
  } catch (e) {
    toast.error('复制失败')
  }
}

// 展开所有节点
const expandAll = () => {
  const addAllPaths = (obj: any, currentPath: string) => {
    expandedPaths.value.add(currentPath)
    if (typeof obj === 'object' && obj !== null) {
      if (Array.isArray(obj)) {
        obj.forEach((_, index) => {
          addAllPaths(obj[index], `${currentPath}[${index}]`)
        })
      } else {
        Object.keys(obj).forEach(key => {
          addAllPaths(obj[key], `${currentPath}.${key}`)
        })
      }
    }
  }

  if (parsedJson.value) {
    addAllPaths(parsedJson.value, 'root')
  }
}

// 折叠所有节点
const collapseAll = () => {
  expandedPaths.value.clear()
  expandedPaths.value.add('root')
}

// 切换路径展开状态
const togglePath = (path: string) => {
  if (expandedPaths.value.has(path)) {
    expandedPaths.value.delete(path)
  } else {
    expandedPaths.value.add(path)
  }
}

// 选择节点
const selectNode = (path: string, data: any) => {
  selectedPath.value = path
  selectedNodeData.value = data
}

// 计算选中节点的属性
const selectedNodeProperties = computed(() => {
  if (!selectedNodeData.value || typeof selectedNodeData.value !== 'object') {
    return {}
  }

  const result: Record<string, any> = {}
  if (Array.isArray(selectedNodeData.value)) {
    result['类型'] = '数组'
    result['长度'] = selectedNodeData.value.length
  } else {
    result['类型'] = '对象'
    result['属性数量'] = Object.keys(selectedNodeData.value).length
  }

  return result
})

// 更新打开状态
const updateOpen = (value: boolean) => {
  emit('update:isOpen', value)
}

// 在组件挂载后初始化 Prism
onMounted(() => {
  // 确保 Prism 正确加载
  nextTick(() => {
    if (codeEditorRef.value) {
      // 如果有初始数据，应用语法高亮
      if (jsonInput.value) {
        codeEditorRef.value.innerHTML = highlightedJson.value
      } else {
        // 如果没有初始数据，设置占位符
        codeEditorRef.value.innerHTML =
          '<span class="text-muted-foreground">请输入 JSON 数据...</span>'
      }
    }
  })
})

// 监听数据变化
watch(() => props.initialData, initData, { immediate: true })

// 监听 jsonInput 变化，延迟解析
watch(jsonInput, () => {
  if (!preventParseUpdate.value) {
    parseJsonDebounced()
  }
})
</script>

<style scoped>
/* 简单的组件样式，避免影响全局布局 */
.font-mono {
  font-family:
    ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
}

/* JSON 编辑器样式 */
.font-mono[contenteditable] {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border: 1px solid hsl(var(--border));
  min-height: 200px;
}

.font-mono[contenteditable]:focus {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* Prism 语法高亮样式 - 限制在组件内 */
:deep(.token.property) {
  color: hsl(var(--primary));
  font-weight: 500;
}

:deep(.token.string) {
  color: rgb(34 197 94);
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.token.number) {
  color: rgb(234 88 12);
  font-weight: 500;
}

:deep(.token.boolean) {
  color: rgb(147 51 234);
  font-weight: 500;
}

:deep(.token.null) {
  color: hsl(var(--muted-foreground));
  font-style: italic;
}

:deep(.token.punctuation) {
  color: hsl(var(--foreground) / 0.7);
}

:deep(.token.keyword) {
  color: rgb(59 130 246);
  font-weight: 500;
}

/* 深色模式样式 */
.dark :deep(.token.string) {
  color: rgb(74 222 128);
}

.dark :deep(.token.number) {
  color: rgb(251 146 60);
}

.dark :deep(.token.boolean) {
  color: rgb(196 181 253);
}

.dark :deep(.token.property) {
  color: rgb(96 165 250);
}

.dark :deep(.token.keyword) {
  color: rgb(147 197 253);
}
</style>
