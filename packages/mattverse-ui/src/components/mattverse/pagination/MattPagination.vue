<template>
  <div class="matt-pagination" :class="[sizeClass, props.class]">
    <div class="flex flex-col sm:flex-row items-center justify-between gap-3 sm:gap-4">
      <!-- 左侧：显示信息和每页条数选择 -->
      <div
        class="flex flex-col sm:flex-row items-center gap-2 sm:gap-4 text-sm text-muted-foreground order-2 sm:order-1"
      >
        <!-- 每页条数选择器 -->
        <div v-if="showSizeChanger" class="flex items-center gap-2">
          <span>显示</span>
          <Select :model-value="String(pageSize)" @update:model-value="handlePageSizeChange">
            <SelectTrigger class="w-20 h-8">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem v-for="size in pageSizeOptions" :key="size" :value="String(size)">
                {{ size }}
              </SelectItem>
            </SelectContent>
          </Select>
          <span>条</span>
        </div>

        <!-- 总数显示 -->
        <div v-if="showTotal" class="whitespace-nowrap">
          {{ totalText }}
        </div>
      </div>

      <!-- 右侧：分页控件 -->
      <div class="flex items-center gap-1 sm:gap-2 order-1 sm:order-2">
        <!-- 简单模式 -->
        <template v-if="simple">
          <Button
            variant="outline"
            :size="buttonSize"
            :disabled="!pageInfo.hasPrev || disabled"
            @click="handlePageChange(current - 1)"
          >
            <ChevronLeft class="h-4 w-4" />
          </Button>

          <div class="flex items-center gap-1 sm:gap-2 px-1 sm:px-2">
            <Input
              :model-value="String(current)"
              @update:model-value="handleQuickJump"
              @keyup.enter="handleQuickJump"
              class="w-12 sm:w-16 h-8 text-center text-xs sm:text-sm"
              :disabled="disabled"
            />
            <span class="text-xs sm:text-sm text-muted-foreground whitespace-nowrap"
              >/ {{ pageInfo.totalPages }}</span
            >
          </div>

          <Button
            variant="outline"
            :size="buttonSize"
            :disabled="!pageInfo.hasNext || disabled"
            @click="handlePageChange(current + 1)"
          >
            <ChevronRight class="h-4 w-4" />
          </Button>
        </template>

        <!-- 完整模式 -->
        <template v-else>
          <!-- 上一页 -->
          <Button
            variant="outline"
            :size="buttonSize"
            :disabled="!pageInfo.hasPrev || disabled"
            @click="handlePageChange(current - 1)"
            class="hidden sm:flex text-muted-foreground"
          >
            <ChevronLeft class="h-4 w-4" />
            上一页
          </Button>

          <!-- 移动端上一页 -->
          <Button
            variant="outline"
            :size="buttonSize"
            :disabled="!pageInfo.hasPrev || disabled"
            @click="handlePageChange(current - 1)"
            class="sm:hidden text-muted-foreground"
          >
            <ChevronLeft class="h-4 w-4" />
          </Button>

          <!-- 页码列表 -->
          <div class="flex items-center gap-0.5 sm:gap-1">
            <!-- 第一页 -->
            <Button
              v-if="showFirstPage"
              variant="outline"
              :size="buttonSize"
              :class="{ 'bg-primary text-primary-foreground text-muted-foreground': current === 1 }"
              :disabled="disabled"
              @click="handlePageChange(1)"
            >
              1
            </Button>

            <!-- 前省略号 -->
            <Button v-if="showPrevEllipsis" variant="ghost" :size="buttonSize" disabled>
              <MoreHorizontal class="h-4 w-4" />
            </Button>

            <!-- 中间页码 -->
            <Button
              v-for="page in middlePages"
              :key="page"
              variant="outline"
              :size="buttonSize"
              :class="{ 'bg-primary text-primary-foreground': current === page }"
              :disabled="disabled"
              @click="handlePageChange(page)"
            >
              {{ page }}
            </Button>

            <!-- 后省略号 -->
            <Button v-if="showNextEllipsis" variant="ghost" :size="buttonSize" disabled>
              <MoreHorizontal class="h-4 w-4" />
            </Button>

            <!-- 最后一页 -->
            <Button
              v-if="showLastPage"
              variant="outline"
              :size="buttonSize"
              :class="{ 'bg-primary text-primary-foreground': current === pageInfo.totalPages }"
              :disabled="disabled"
              @click="handlePageChange(pageInfo.totalPages)"
            >
              {{ pageInfo.totalPages }}
            </Button>
          </div>

          <!-- 下一页 -->
          <Button
            variant="outline"
            :size="buttonSize"
            :disabled="!pageInfo.hasNext || disabled"
            @click="handlePageChange(current + 1)"
            class="hidden sm:flex text-muted-foreground"
          >
            下一页
            <ChevronRight class="h-4 w-4" />
          </Button>

          <!-- 移动端下一页 -->
          <Button
            variant="outline"
            :size="buttonSize"
            :disabled="!pageInfo.hasNext || disabled"
            @click="handlePageChange(current + 1)"
            class="sm:hidden"
          >
            <ChevronRight class="h-4 w-4" />
          </Button>

          <!-- 快速跳转 -->
          <div v-if="showQuickJumper" class="hidden sm:flex items-center gap-2 ml-2">
            <span class="text-sm text-muted-foreground whitespace-nowrap">跳至</span>
            <Input
              v-model="jumpValue"
              @keyup.enter="handleQuickJumpEnter"
              class="w-16 h-8 text-center"
              :disabled="disabled"
              placeholder="页码"
            />
            <span class="text-sm text-muted-foreground">页</span>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-vue-next'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import type { MattPaginationProps, MattPaginationEmits, PageInfo } from './types'

const props = withDefaults(defineProps<MattPaginationProps>(), {
  pageSizeOptions: () => [10, 20, 50, 100],
  showSizeChanger: true,
  showQuickJumper: false,
  showTotal: true,
  showLessItems: false,
  simple: false,
  disabled: false,
  size: 'default',
  maxPageItems: 5,
})

const emit = defineEmits<MattPaginationEmits>()

// 响应式数据
const jumpValue = ref('')

// 计算属性
const pageInfo = computed<PageInfo>(() => {
  const totalPages = Math.ceil(props.total / props.pageSize)
  const startIndex = (props.current - 1) * props.pageSize + 1
  const endIndex = Math.min(props.current * props.pageSize, props.total)

  return {
    current: props.current,
    pageSize: props.pageSize,
    total: props.total,
    totalPages,
    startIndex,
    endIndex,
    hasNext: props.current < totalPages,
    hasPrev: props.current > 1,
  }
})

const sizeClass = computed(() => {
  const sizeMap = {
    sm: 'matt-pagination--sm',
    default: 'matt-pagination--default',
    lg: 'matt-pagination--lg',
  }
  return sizeMap[props.size]
})

const buttonSize = computed(() => {
  const sizeMap = {
    sm: 'sm' as const,
    default: 'sm' as const,
    lg: 'default' as const,
  }
  return sizeMap[props.size]
})

const totalText = computed(() => {
  if (props.totalTemplate) {
    return props.totalTemplate(props.total, [pageInfo.value.startIndex, pageInfo.value.endIndex])
  }

  return `共 ${props.total} 条`
})

// 页码显示逻辑
const showFirstPage = computed(() => {
  const { totalPages } = pageInfo.value
  const { current } = props

  // 当总页数小于等于最大显示页数时，不单独显示第一页，由 middlePages 统一处理
  if (totalPages <= props.maxPageItems) {
    return false
  }

  // 当前页大于3时，显示第一页
  return totalPages > 1 && current > 3
})

const showLastPage = computed(() => {
  const { totalPages } = pageInfo.value
  const { current } = props

  // 当总页数小于等于最大显示页数时，不单独显示最后一页，由 middlePages 统一处理
  if (totalPages <= props.maxPageItems) {
    return false
  }

  // 当前页小于倒数第3页时，显示最后一页
  return totalPages > 1 && current < totalPages - 2
})

const showPrevEllipsis = computed(() => {
  return props.current > 3 && pageInfo.value.totalPages > props.maxPageItems
})

const showNextEllipsis = computed(() => {
  return (
    props.current < pageInfo.value.totalPages - 2 && pageInfo.value.totalPages > props.maxPageItems
  )
})

const middlePages = computed(() => {
  const { totalPages } = pageInfo.value
  const { current } = props
  const maxItems = props.maxPageItems

  // 当总页数小于等于最大显示页数时，显示所有页码
  if (totalPages <= maxItems) {
    return Array.from({ length: totalPages }, (_, i) => i + 1)
  }

  const half = Math.floor(maxItems / 2)
  let start = Math.max(1, current - half)
  let end = Math.min(totalPages, start + maxItems - 1)

  if (end - start + 1 < maxItems) {
    start = Math.max(1, end - maxItems + 1)
  }

  // 避免与首页和尾页重复
  if (start === 1) {
    start = 2
  }
  if (end === totalPages) {
    end = totalPages - 1
  }

  const pages = []
  for (let i = start; i <= end; i++) {
    if (i > 1 && i < totalPages) {
      pages.push(i)
    }
  }

  return pages
})

// 方法
const handlePageChange = (page: number) => {
  if (page < 1 || page > pageInfo.value.totalPages || page === props.current || props.disabled) {
    return
  }

  emit('update:current', page)
  emit('change', page, props.pageSize)
}

const handlePageSizeChange = (sizeStr: string) => {
  const newSize = Number(sizeStr)
  if (newSize === props.pageSize || props.disabled) {
    return
  }

  // 计算新的页码，确保数据连续性
  const newCurrent = Math.ceil(((props.current - 1) * props.pageSize) / newSize) + 1
  const maxPage = Math.ceil(props.total / newSize)
  const finalCurrent = Math.min(newCurrent, maxPage) || 1

  emit('update:pageSize', newSize)
  emit('update:current', finalCurrent)
  emit('showSizeChange', finalCurrent, newSize)
  emit('change', finalCurrent, newSize)
}

const handleQuickJump = (value: string) => {
  const page = Number(value)
  if (page && page >= 1 && page <= pageInfo.value.totalPages) {
    handlePageChange(page)
  }
}

const handleQuickJumpEnter = () => {
  const page = Number(jumpValue.value)
  if (page && page >= 1 && page <= pageInfo.value.totalPages) {
    handlePageChange(page)
    jumpValue.value = ''
  }
}
</script>

<style scoped>
.matt-pagination {
  @apply w-full;
}

.matt-pagination--sm {
  @apply text-xs;
}

.matt-pagination--default {
  @apply text-sm;
}

.matt-pagination--lg {
  @apply text-base;
}

.matt-pagination .flex {
  @apply flex-wrap;
}

/* 移动端适配 */
@media (max-width: 640px) {
  .matt-pagination {
    @apply text-xs;
  }

  .matt-pagination .justify-between {
    @apply justify-center;
  }

  /* 移动端按钮尺寸调整 */
  .matt-pagination .h-8 {
    @apply h-7;
  }

  .matt-pagination .w-20 {
    @apply w-16;
  }

  /* 移动端页码按钮优化 */
  .matt-pagination [class*='gap-'] {
    @apply gap-0.5;
  }

  /* 隐藏移动端不必要的文本 */
  .matt-pagination .whitespace-nowrap {
    @apply text-xs;
  }
}

/* 平板适配 */
@media (min-width: 641px) and (max-width: 1024px) {
  .matt-pagination {
    @apply text-sm;
  }

  /* 平板端适当调整间距 */
  .matt-pagination .gap-4 {
    @apply gap-3;
  }

  .matt-pagination .gap-2 {
    @apply gap-1.5;
  }
}

/* 大屏幕优化 */
@media (min-width: 1025px) {
  .matt-pagination .gap-1 {
    @apply gap-2;
  }
}

/* 超小屏幕适配 */
@media (max-width: 480px) {
  .matt-pagination {
    @apply text-xs;
  }

  /* 超小屏幕下进一步压缩 */
  .matt-pagination .h-7 {
    @apply h-6 px-1;
  }

  .matt-pagination .w-16 {
    @apply w-12;
  }

  /* 超小屏幕下的页码显示优化 */
  .matt-pagination .flex-col {
    @apply gap-2;
  }
}
</style>
