<template>
  <div class="node-icon-wrapper">
    <!-- SVG 图标 -->
    <MattSvg 
      v-if="iconType === 'svg'" 
      :name="iconValue" 
      :class-name="className"
    />
    
    <!-- Icon 图标 -->
    <Icon 
      v-else-if="iconType === 'icon'" 
      :icon="iconValue" 
      :class="cn('w-6 h-6', className)"
    />
    
    <!-- Lucide 图标 -->
    <MattIcon 
      v-else-if="iconType === 'lucide'" 
      :name="iconValue" 
      :class="cn('w-6 h-6', className)"
    />
    
    <!-- 默认图标 -->
    <Icon 
      v-else 
      icon="material-symbols:help-outline" 
      :class="cn('w-6 h-6', className)"
    />
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Icon } from '@iconify/vue'
import MattSvg from './MattSvg.vue'
import MattIcon from './MattIcon.vue'
import { cn } from '../../../../lib/utils'

interface Props {
  icon: string | { type: string; value: string }
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  className: '',
})

// 解析图标类型和值
const iconType = computed(() => {
  if (typeof props.icon === 'string') {
    return 'icon'
  }
  return props.icon.type || 'icon'
})

const iconValue = computed(() => {
  if (typeof props.icon === 'string') {
    return props.icon
  }
  return props.icon.value || ''
})
</script>

<style scoped>
.node-icon-wrapper {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
</style>
